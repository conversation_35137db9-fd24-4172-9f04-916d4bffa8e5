import React from 'react';
import { Tabs } from 'expo-router';
import { View, TouchableOpacity, StyleSheet, Text, I18nManager } from 'react-native';
import { colors } from '@/constants/colors';
import {
  Home,
  ListTodo,
  User,
  Plus,
  BarChart3,
  Leaf,
  Rabbit,
  Tractor,
  TreeDeciduous,
  Wheat,
  X,
  QrCode,
  MapPinOffIcon,
  QrCodeIcon,
  User2,
} from 'lucide-react-native';
import { useAuthStore } from '@/store/auth-store';
import { router } from 'expo-router';
import { useTranslation } from '@/i18n/useTranslation';

export default function TabLayout() {
  const { t, isRTL } = useTranslation();
  const { user } = useAuthStore();
  const [showAddMenu, setShowAddMenu] = React.useState(false);

  const toggleAddMenu = () => {
    setShowAddMenu(!showAddMenu);
  };

  const hideAddMenu = () => {
    setShowAddMenu(false);
  };

  const renderAddButton = () => {
    return (
      <TouchableOpacity
        style={styles.addButton}
        onPress={toggleAddMenu}
      >
        {showAddMenu ? (
          <X size={24} color={colors.white} />
        ) : (
          <Plus size={24} color={colors.white} />
        )}
      </TouchableOpacity>
    );
  };

  // Check if user is a caretaker or admin to hide reports tab
  const isCaretaker = user?.role === 'caretaker';
  const isAdmin = user?.role === 'admin';
  const shouldHideReports = isCaretaker || isAdmin;

  const handleOpenScanner = () => {
    router.push('/scanner');
  };

  return (
    <>
      {showAddMenu && (
        <TouchableOpacity
          style={styles.overlay}
          activeOpacity={0.5}
          onPress={hideAddMenu}
        />
      )}

      {showAddMenu && (
        <View style={styles.addMenuContainer}>
          <TouchableOpacity
            style={[styles.addMenuItem, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}
            onPress={() => {
              hideAddMenu();
              router.push('/plant/create');
            }}
          >
            <View style={[styles.addMenuItemIcon, { backgroundColor: colors.success }]}>
              <Leaf size={20} color={colors.white} />
            </View>
            <Text style={[styles.addMenuItemText, {marginRight: isRTL ? 8 : 0,  textAlign: isRTL ? 'right' : 'left' }]}>
              {t('add.plant', 'Add Plant')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
           style={[styles.addMenuItem, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}
            onPress={() => {
              hideAddMenu();
              router.push('/animal/create');
            }}
          >
            <View style={[styles.addMenuItemIcon, { backgroundColor: colors.warning }]}>
              <Rabbit size={20} color={colors.white} />
            </View>
            <Text style={[styles.addMenuItemText, {marginRight: isRTL ? 8 : 0,  textAlign: isRTL ? 'right' : 'left' }]}>
              {t('add.animal', 'Add Animal')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
           style={[styles.addMenuItem, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}
            onPress={() => {
              hideAddMenu();
              router.push('/field/create');
            }}
          >
            <View style={[styles.addMenuItemIcon, { backgroundColor: colors.info }]}>
              <QrCode size={20} color={colors.white} />
            </View>
            <Text style={[styles.addMenuItemText, {marginRight: isRTL ? 8 : 0,  textAlign: isRTL ? 'right' : 'left' }]}>
              {t('add.field', 'Add Field')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.addMenuItem, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}
            onPress={() => {
              hideAddMenu();
              router.push('/garden/create');
            }}
          >
            <View style={[styles.addMenuItemIcon, { backgroundColor: colors.primary }]}>
              <TreeDeciduous size={20} color={colors.white} />
            </View>
            <Text style={[styles.addMenuItemText, {marginRight: isRTL ? 8 : 0,  textAlign: isRTL ? 'right' : 'left' }]}>
              {t('add.garden', 'Add Garden')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.addMenuItem, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}
            onPress={() => {
              hideAddMenu();
              router.push('/equipment/create');
            }}
          >
            <View style={[styles.addMenuItemIcon, { backgroundColor: colors.secondary }]}>
              <Tractor size={20} color={colors.white} />
            </View>
            <Text style={[styles.addMenuItemText, {marginRight: isRTL ? 8 : 0,  textAlign: isRTL ? 'right' : 'left' }]}>
              {t('add.equipment', 'Add Equipment')}
            </Text>
          </TouchableOpacity>

          {/* <TouchableOpacity
            style={styles.addMenuItem}
            onPress={() => {
              hideAddMenu();
              router.push('/yield/create');
            }}
          >
            <View style={[styles.addMenuItemIcon, { backgroundColor: colors.danger }]}>
              <Wheat size={20} color={colors.white} />
            </View>
            <Text style={[styles.addMenuItemText, { textAlign: isRTL ? 'right' : 'left' }]}>
              {t('add.yield', 'Add Yield')}
            </Text>
          </TouchableOpacity> */}

          <TouchableOpacity
             style={[styles.addMenuItem, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}
            onPress={() => {
              hideAddMenu();
              router.push('/task/create');
            }}
          >
            <View style={[styles.addMenuItemIcon, { backgroundColor: colors.gray[700] }]}>
              <ListTodo size={20} color={colors.white} />
            </View>
            <Text style={[styles.addMenuItemText, {marginRight: isRTL ? 8 : 0, textAlign: isRTL ? 'right' : 'left' }]}>
              {t('add.task', 'Add Task')}
            </Text>
          </TouchableOpacity>
        </View>
      )}

      <Tabs
        screenOptions={{
          tabBarActiveTintColor: colors.primary,
          tabBarInactiveTintColor: colors.gray[500],
          tabBarStyle: {
            height: 60,
            paddingBottom: 8,
            paddingTop: 8,
          },
          tabBarLabelStyle: {
            writingDirection: 'ltr',
            textAlign: 'left',
             fontSize: 12,
          },
          headerShown: true,

          
          // Remove RTL-specific tab bar options
        }}
      >
        <Tabs.Screen
          name="index"
          options={{
            title: t('tabs.home', 'Home'),
            tabBarIcon: ({ color }) => <Home size={24} color={color} />,
          }}
        />

        <Tabs.Screen
          name="tasks"
          options={{
            title: t('tabs.tasks', 'Tasks'),
            tabBarIcon: ({ color }) => <ListTodo size={24} color={color} />,
          }}
        />

        <Tabs.Screen
          name="map"
          options={{
            title: t('tabs.map', 'Map'),
            tabBarIcon: ({ color }) => <MapPinOffIcon size={24} color={color} />,
          }}
        // listeners={{
        //   tabPress: (e) => {
        //     // Prevent default action and open scanner directly
        //     e.preventDefault();
        //     handleOpenScanner();
        //   },
        // }}
        />

        <Tabs.Screen
          name="add"
          options={{
            title: '',
            tabBarButton: () => renderAddButton(),
          }}
          listeners={{
            tabPress: (e) => {
              // Prevent default action
              e.preventDefault();
              toggleAddMenu();
            },
          }}
        />
        <Tabs.Screen
          name="scanner"
          options={{
            title: t('tabs.scanner', 'Scanner'),
            tabBarIcon: ({ color }) => <QrCodeIcon size={24} color={color} />,
          }}
          listeners={{
            tabPress: (e) => {
              // Prevent default action and open scanner directly
              e.preventDefault();
              handleOpenScanner();
            },
          }}
        />


        {/* Only show reports tab for owners */}
        {!shouldHideReports && (
          <Tabs.Screen
            name="reports"
            options={{
              title: t('tabs.reports', 'Reports'),
              tabBarIcon: ({ color }) => <BarChart3 size={24} color={color} />,
            }}
          />
        )}

        <Tabs.Screen
          name="profile"
          options={{
            title: t('tabs.profile', 'Profile'),
            tabBarIcon: ({ color }) => <User2 size={24} color={color} />,
          }}

        />
      </Tabs>
    </>
  );
}

const styles = StyleSheet.create({
  addButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 28,
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 1,
  },
  addMenuContainer: {
    position: 'absolute',
    bottom: 80,
    left: 16,
    right: 16,
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 16,
    zIndex: 2,
    shadowColor: colors.gray[800],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
  },
  addMenuItem: {
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[100],
  },
  addMenuItemIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: I18nManager.isRTL ? 0 : 16,
    marginLeft: I18nManager.isRTL ? 16 : 0,
  },
  addMenuItemText: {
    fontSize: 16,
    color: colors.gray[800],
    fontWeight: '500',
    flex: 1,
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
});
