import { create } from 'zustand';
// import { collection, getDocs } from 'firebase/firestore';
// import { database } from '@/firebase/config';
import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDocs,
  query,
  where,
  serverTimestamp,
  Timestamp,
  getDoc,
  setDoc,
  arrayUnion
} from 'firebase/firestore';
import { firestore } from '@/firebase/config';
import { toCamelCase } from '@/utils/util';
import { useTranslation } from '@/i18n/useTranslation';
import { t } from '@/i18n';
export interface Lookup {
  label: string;
  value: string;
  [key: string]: any; // for other properties
}

export interface LookupCategory {
  id: string;
  name: string;
  // other properties
}

interface LookupState {
  lookups: { [category: string]: Lookup[] };
  lookupsList: any[];
  lookupCategories: LookupCategory[];
  isLoading: boolean;
  fetchLookups: () => Promise<void>;
  getLookupsByCategory: (category: string) => Lookup[];
  getLookupsByCategoryParssedData: (category: string,transkey:string) => Lookup[];
  getLookupValueById: (category: string, id: string) => Lookup | null;
  validateLookupId: (category: string, id: string) => boolean;
}

export const useLookupStore = create<LookupState>((set, get) => ({
  lookups: {},
  lookupsList: [],
  lookupCategories: [],
  isLoading: false,


  
  fetchLookups: async () => {
    // Prevent re-fetching if already loading or data exists
    if (get().isLoading || Object.keys(get().lookups).length > 0) {
      return;
    }

    set({ isLoading: true });
    try {
      // 1. Fetch all lookup categories from 'lookupCategories' collection
      const categoriesSnapshot = await getDocs(collection(firestore, 'lookupCategories'));
      const categories = categoriesSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as LookupCategory[];
      set({ lookupCategories: categories });

      // 2. Fetch all individual lookup items from 'lookups' collection
      const lookupsSnapshot = await getDocs(collection(firestore, 'lookups'));
      const allLookups = lookupsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

      set({ lookupsList: allLookups })
      // 3. Group the lookups by their 'category' field
      const groupedLookups: { [category: string]: Lookup[] } = {};
      allLookups.forEach(lookup => {
        const categoryKey = toCamelCase(lookup.categoryName); // e.g., 'taskPriorities', 'taskCategories'
        if (!categoryKey) return;

        if (!groupedLookups[categoryKey]) {
          groupedLookups[categoryKey] = [];
        }
        groupedLookups[categoryKey].push(lookup as Lookup);
      });
      // const {taskCategory,taskPriority}=groupedLookups
      // console.log({ groupedLookups })
      // console.log({groupedLookups }c{categories},"------------------------",{allLookups})
      set({ lookups: groupedLookups, isLoading: false });
    } catch (error) {
      console.error("Error fetching lookups:", error);
      set({ isLoading: false });
    }
  },

  getLookupsByCategory: (category: string) => {
    return get().lookups[category] || [];
  },
  getLookupsByCategoryParssedData: (category: string,transkey:string) => {

    // const { t}=useTranslation()
    const lookups = get().lookups[category] || [];
    return lookups.map((item: any) => ({
      label: t(`${transkey}${item.title}`),
      value: item.id,
      icon: item.photoUrl,
      parentId: item.parentId,
    }));
  },

  // Get lookup item by ID from a specific category
  getLookupValueById: (category: string, id: string) => {
    const lookups = get().lookups[category] || [];
    return lookups.find((item: any) => item.id === id) || null;
  },

  // Validate if an ID exists in a specific category
  validateLookupId: (category: string, id: string) => {
    const lookups = get().lookups[category] || [];
    return lookups.some((item: any) => item.id === id);
  }
}));