import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  FlatList,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { SuggestedAction } from '@/services/ai-assistant';
import { useFarmStore } from '@/store/farm-store';
import EntityDetailsForm from './EntityDetailsForm';
import { colors } from '@/constants/colors';
import { StyleSheet } from 'react-native';
import { generateUniqueItemId } from '@/utils/qrcode';
import { validateEntityData, convertAIDataToLookupIds } from '@/utils/lookup-validation';

interface ActionConfirmationModalProps {
  visible: boolean;
  actions: SuggestedAction[];
  onClose: () => void;
  onConfirm: (confirmedActions: SuggestedAction[]) => void;
}

export default function ActionConfirmationModal({
  visible,
  actions,
  onClose,
  onConfirm,
}: ActionConfirmationModalProps) {
  const [selectedAction, setSelectedAction] = useState<SuggestedAction | null>(null);
  const [completedActions, setCompletedActions] = useState<SuggestedAction[]>([]);
  const [isExecuting, setIsExecuting] = useState(false);

  const {
    addAnimal,
    updateAnimal,
    addPlant,
    updatePlant,
    addGarden,
    updateGarden,
    addField,
    updateField,
    addEquipment,
    updateEquipment,
    currentFarm,
  } = useFarmStore();

  const handleActionSelect = (action: SuggestedAction) => {
    setSelectedAction(action);
  };

  const handleFormSave = async (updatedData: any) => {
    if (!selectedAction || !currentFarm) return;

    try {
      setIsExecuting(true);
      const data = { ...updatedData, farmId: currentFarm.id };

      await executeAction({ ...selectedAction, data });

      // Mark action as completed
      setCompletedActions(prev => [...prev, { ...selectedAction, data }]);
      setSelectedAction(null);

      Alert.alert('Success', 'Entity saved successfully!');
    } catch (error) {
      console.error('Error saving entity:', error);
      Alert.alert('Error', 'Failed to save entity');
    } finally {
      setIsExecuting(false);
    }
  };

  const executeAction = async (action: SuggestedAction) => {
    let data = { ...action.data };

    // Convert AI-generated text values to proper lookup IDs
    const { convertedData, conversionLog } = convertAIDataToLookupIds(action.entity, data);
    data = convertedData;

    // Log conversions for debugging
    if (conversionLog.length > 0) {
      console.log('AI Data Conversions:', conversionLog);
    }

    // Validate the converted data
    const { sanitizedData, validationErrors, isValid } = validateEntityData(action.entity, data);

    if (!isValid) {
      console.warn('Entity data validation errors:', validationErrors);
      Alert.alert('Data Validation Error', `Some data could not be validated: ${validationErrors.join(', ')}`);
      // Continue with sanitized data (invalid fields removed)
    }

    // Use the sanitized data
    data = sanitizedData;

    // Generate identification ID for each entity type before saving
    if (action.type === 'create') {
      switch (action.entity) {
        case 'animal':
          data.identificationID = data.identificationID || generateUniqueItemId('ANM');
          break;
        case 'plant':
          data.identificationID = data.identificationID || generateUniqueItemId('PLT');
          break;
        case 'garden':
          data.identificationID = data.identificationID || generateUniqueItemId('GRD');
          break;
        case 'field':
          data.identificationID = data.identificationID || generateUniqueItemId('FLD');
          break;
        case 'equipment':
          data.identificationID = data.identificationID || generateUniqueItemId('EQP');
          break;
      }
    }

    switch (action.entity) {
      case 'animal':
        if (action.type === 'create') {
          await addAnimal(data);
        } else if (action.type === 'update') {
          await updateAnimal(data.id, data);
        }
        break;

      case 'plant':
        if (action.type === 'create') {
          console.log({ data })
          await addPlant(data);
        } else if (action.type === 'update') {
          await updatePlant(data.id, data);
        }
        break;

      case 'garden':
        if (action.type === 'create') {
          await addGarden(data);
        } else if (action.type === 'update') {
          await updateGarden(data.id, data);
        }
        break;

      case 'field':
        if (action.type === 'create') {
          await addField(data);
        } else if (action.type === 'update') {
          await updateField(data.id, data);
        }
        break;

      case 'equipment':
        if (action.type === 'create') {
          await addEquipment(data);
        } else if (action.type === 'update') {
          await updateEquipment(data.id, data);
        }
        break;
    }
  };

  const handleFinish = () => {
    onConfirm(completedActions);
    setCompletedActions([]);
  };

  const getActionIcon = (action: SuggestedAction) => {
    const iconMap = {
      animal: 'paw',
      plant: 'leaf',
      garden: 'flower',
      field: 'grid',
      equipment: 'construct',
    };
    return iconMap[action.entity] || 'help';
  };

  const getActionColor = (action: SuggestedAction) => {
    const colorMap = {
      create: colors.success,
      update: colors.warning,
      delete: colors.danger,
    };
    return colorMap[action.type];
  };

  const isActionCompleted = (actionId: string) => {
    return completedActions.some(action => action.id === actionId);
  };

  const renderAction = ({ item }: { item: SuggestedAction }) => {
    const isCompleted = isActionCompleted(item.id);
    
    return (
      <TouchableOpacity
        style={[
          styles.actionItem,
          isCompleted && styles.actionItemCompleted
        ]}
        onPress={() => !isCompleted && handleActionSelect(item)}
        disabled={isCompleted}
      >
        <View style={styles.actionHeader}>
          <View style={styles.actionIconContainer}>
            <Ionicons
              name={isCompleted ? 'checkmark-circle' : getActionIcon(item) as any}
              size={20}
              color={isCompleted ? colors.success : getActionColor(item)}
            />
          </View>

          <View style={styles.actionInfo}>
            <Text style={styles.actionTitle}>{item.title}</Text>
            <Text style={styles.actionDescription}>{item.description}</Text>
            <Text style={styles.actionMeta}>
              {item.type.toUpperCase()} {item.entity.toUpperCase()} • {Math.round(item.confidence * 100)}% confidence
            </Text>
          </View>

          <View style={styles.actionStatus}>
            {isCompleted ? (
              <Text style={styles.completedText}>✓ Saved</Text>
            ) : (
              <Ionicons name="chevron-forward" size={20} color={colors.gray[500]} />
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  // Show detailed form for selected action
  if (selectedAction) {
    return (
      <Modal visible={visible} animationType="slide" presentationStyle="fullScreen">
        <EntityDetailsForm
          action={selectedAction}
          onSave={handleFormSave}
          onCancel={() => setSelectedAction(null)}
        />
      </Modal>
    );
  }

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>AI Suggestions</Text>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color={colors.gray[800]} />
          </TouchableOpacity>
        </View>

        <Text style={styles.subtitle}>
          Tap on each suggestion to review and edit details before saving:
        </Text>

        <FlatList
          data={actions}
          renderItem={renderAction}
          keyExtractor={item => item.id}
          style={styles.actionsList}
        />

        <View style={styles.footer}>
          <View style={styles.progressContainer}>
            <Text style={styles.progressText}>
              {completedActions.length} of {actions.length} completed
            </Text>
          </View>
          
          <TouchableOpacity
            style={[
              styles.finishButton,
              completedActions.length === 0 && styles.finishButtonDisabled
            ]}
            onPress={handleFinish}
            disabled={completedActions.length === 0}
          >
            <Text style={styles.finishButtonText}>
              {completedActions.length === actions.length ? 'Done' : 'Finish Later'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.gray[800],
  },
  subtitle: {
    fontSize: 16,
    color: colors.gray[600],
    padding: 16,
    paddingBottom: 8,
  },
  actionsList: {
    flex: 1,
  },
  actionItem: {
    backgroundColor: colors.white,
    marginHorizontal: 16,
    marginVertical: 4,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  actionItemCompleted: {
    backgroundColor: colors.gray[100],
    borderColor: colors.success,
  },
  actionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  actionInfo: {
    flex: 1,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 4,
  },
  actionDescription: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 4,
  },
  actionMeta: {
    fontSize: 12,
    color: colors.gray[500],
    fontWeight: '500',
  },
  actionStatus: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  completedText: {
    fontSize: 12,
    color: colors.success,
    fontWeight: '600',
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  progressContainer: {
    alignItems: 'center',
    marginBottom: 12,
  },
  progressText: {
    fontSize: 14,
    color: colors.gray[600],
  },
  finishButton: {
    padding: 16,
    borderRadius: 12,
    backgroundColor: colors.primary,
    alignItems: 'center',
  },
  finishButtonDisabled: {
    backgroundColor: colors.gray[400],
  },
  finishButtonText: {
    fontSize: 16,
    color: colors.white,
    fontWeight: '600',
  },
});

