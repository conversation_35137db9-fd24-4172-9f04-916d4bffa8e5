import React, { useEffect, useState, useTransition } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  FlatList,
  Dimensions,
  SafeAreaView,
  Modal,
  TextInput,
  Alert,
} from 'react-native';
import { useLocalSearchParams, Stack, router } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import {
  MapPin,
  Users,
  Leaf,
  TreeDeciduous,
  Tractor,
  BarChart3,
  ChevronRight,
  Plus,
  Rabbit,
  Calendar,
  ArrowRight,
  Edit,
  Share,
  MoreHorizontal,
  X,
  Check,
  UserPlus,
  Trash2,
  ChevronDown,
  ChevronLeft,
} from 'lucide-react-native';
import { Farm, Field, Garden, Animal, Equipment, Task, User } from '@/types';
import Button from '@/components/Button';
import TaskCard from '@/components/TaskCard';
import { useLanguage } from '@/i18n/translations/LanguageProvider';
import { useTranslation } from '@/i18n/useTranslation';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import TeamMemberCard from '@/components/TeamMemberCard';

const { width } = Dimensions.get('window');

export default function FarmDetailsScreen() {
  const { id } = useLocalSearchParams();
  const {
    farms,
    fields,
    gardens,
    animals,
    inventoryEquipment,
    tasks,
    fetchFarms,
    fetchFields,
    fetchGardens,
    fetchAnimals,
    fetchEquipmentFromInventory,
    fetchTasks,
    updateFarm,
    deleteFarm,
    isLoading
  } = useFarmStore();

  const {
    user,
    getUsersByFarm
  } = useAuthStore();
  const { t, isRTL } = useTranslation();
  const [farm, setFarm] = useState<Farm | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'fields' | 'gardens' | 'animals' | 'equipment' | 'tasks' | 'team'>('overview');
  const [showOptions, setShowOptions] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showInviteModal, setShowInviteModal] = useState(false);

  // Edit farm state
  const [editName, setEditName] = useState('');
  const [editLocation, setEditLocation] = useState('');
  const [editSize, setEditSize] = useState('');
  const [editSizeUnit, setEditSizeUnit] = useState<'acres' | 'hectares'>('acres');
  const [editType, setEditType] = useState<'crop' | 'livestock' | 'mixed' | 'orchard'>('mixed');
  const [editDescription, setEditDescription] = useState('');

  // Invite user state
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState<'admin' | 'caretaker'>('caretaker');

  // Farm users state
  const [farmUsers, setFarmUsers] = useState<User[]>([]);
  const [loadingFarmUsers, setLoadingFarmUsers] = useState(false);

  useEffect(() => {
    if (id) {
      const farmId = Array.isArray(id) ? id[0] : id;

      // Find farm in store
      const foundFarm = farms.find(f => f.id === farmId);
      if (foundFarm) {
        setFarm(foundFarm);

        // Set edit form values
        setEditName(foundFarm.name);
        setEditLocation(foundFarm.location || '');
        setEditSize(foundFarm.size?.toString() || '');
        setEditSizeUnit(foundFarm.sizeUnit || 'acres');
        setEditType(foundFarm.type || 'mixed');
        setEditDescription(foundFarm.description || '');

        // Load farm users
        loadFarmUsers(farmId);
      }

      // Fetch farm data
      fetchFields(farmId);
      fetchGardens(farmId);
      fetchAnimals(farmId);
      fetchEquipmentFromInventory(farmId);
      fetchTasks(farmId);
    }
  }, [id, farms]);

  const loadFarmUsers = async (farmId: string) => {
    setLoadingFarmUsers(true);
    try {
      const users = await getUsersByFarm(farmId);
      setFarmUsers(users);
    } catch (error) {
      console.error('Error loading farm users:', error);
    } finally {
      setLoadingFarmUsers(false);
    }
  };

  if (!farm) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  const handleEditFarm = async () => {
    if (!editName) {
      Alert.alert('Error', 'Farm name is required');
      return;
    }

    try {
      const updatedFarm = {
        ...farm,
        name: editName,
        location: editLocation,
        size: editSize ? parseFloat(editSize) : undefined,
        sizeUnit: editSizeUnit,
        type: editType,
        description: editDescription,
      };

      await updateFarm(farm.id, updatedFarm);
      setShowEditModal(false);
      Alert.alert('Success', 'Farm updated successfully');
    } catch (error) {
      console.error('Error updating farm:', error);
      Alert.alert('Error', 'Failed to update farm');
    }
  };

  const handleDeleteFarm = async () => {
    try {
      await deleteFarm(farm.id);
      router.replace('/');
    } catch (error) {
      console.error('Error deleting farm:', error);
      Alert.alert('Error', 'Failed to delete farm');
    }
  };

  const handleInviteUser = () => {
    if (!inviteEmail) {
      Alert.alert('Error', 'Email is required');
      return;
    }

    // Navigate to the invite user screen with pre-filled farm
    router.push({
      pathname: '/user/invite',
      params: { farmId: farm.id, email: inviteEmail, role: inviteRole }
    });
  };

  const renderOverviewTab = () => (
    <ScrollView contentContainerStyle={styles.overviewScrollContent}>
      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <View style={styles.statIconContainer}>
            <Leaf size={20} color={colors.primary} />
          </View>
          <Text style={styles.statValue}>{fields.length}</Text>
          <Text style={styles.statLabel}>{t('entity.farm.fields')}</Text>
        </View>

        <View style={styles.statCard}>
          <View style={styles.statIconContainer}>
            <TreeDeciduous size={20} color={colors.success} />
          </View>
          <Text style={styles.statValue}>{gardens.length}</Text>
          <Text style={styles.statLabel}>{t('entity.farm.Gardens')}</Text>
        </View>

        <View style={styles.statCard}>
          <View style={styles.statIconContainer}>
            <Rabbit size={20} color={colors.warning} />
          </View>
          <Text style={styles.statValue}>{animals.length}</Text>
          <Text style={styles.statLabel}>{t('entity.farm.Animals')}</Text>
        </View>

        <View style={styles.statCard}>
          <View style={styles.statIconContainer}>
            <Calendar size={20} color={colors.danger} />
          </View>
          <Text style={styles.statValue}>{tasks.filter(t => t.status === 'pending').length}</Text>
          <Text style={styles.statLabel}>{t('entity.farm.Tasks')}</Text>
        </View>
      </View>

      <View style={styles.quickActionsContainer}>
        <Text style={[styles.sectionTitle, isRTL && styles.rtlTextAlign]}>{t('entity.farm.quickActions')}</Text>
        <View style={styles.quickActions}>
          <TouchableOpacity
            style={[styles.quickActionButton, isRTL && { flexDirection: 'row-reverse' }]}
            onPress={() => router.push('/task/create')}
          >
            <View style={[styles.quickActionIcon, { backgroundColor: colors.primary }]}>
              <Calendar size={20} color={colors.white} />
            </View>
            <Text style={[styles.quickActionText, { textAlign: 'right', marginRight: 8 }]}>{t('entity.farm.addTask')}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.quickActionButton, isRTL && { flexDirection: 'row-reverse' }]}
            onPress={() => router.push('/field/create')}
          >
            <View style={[styles.quickActionIcon, { backgroundColor: colors.success }]}>
              <Leaf size={20} color={colors.white} />
            </View>
            <Text style={[styles.quickActionText, { textAlign: 'right', marginRight: 8 }]}>{t('entity.farm.addField')}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.quickActionButton, isRTL && { flexDirection: 'row-reverse' }]}
            onPress={() => router.push('/garden/create')}
          >
            <View style={[styles.quickActionIcon, { backgroundColor: colors.info }]}>
              <TreeDeciduous size={20} color={colors.white} />
            </View>
            <Text style={[styles.quickActionText, { textAlign: 'right', marginRight: 8 }]}>{t('entity.farm.addGarden')}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.quickActionButton, isRTL && { flexDirection: 'row-reverse' }]}
            onPress={() => router.push('/animal/create')}
          >
            <View style={[styles.quickActionIcon, { backgroundColor: colors.warning }]}>
              <Rabbit size={20} color={colors.white} />
            </View>
            <Text style={[styles.quickActionText, { textAlign: 'right', marginRight: 8 }]}>{t('entity.farm.addAnimal')}</Text>
          </TouchableOpacity>
        </View>
      </View>

      {tasks.filter(t => t.status === 'pending').length > 0 && (
        <View style={styles.upcomingTasksContainer}>
          <View style={[styles.sectionHeader, isRTL && { flexDirection: 'row-reverse' }]}>
            <Text style={styles.sectionTitle}>{t('entity.farm.upcomingTasks')}</Text>
            <TouchableOpacity onPress={() => setActiveTab('tasks')}>
              <Text style={styles.seeAllText}>{t('entity.farm.viewAllTasks')}</Text>
            </TouchableOpacity>
          </View>

          {tasks
            .filter(t => t.status === 'pending')
            .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime())
            .slice(0, 3)
            .map(task => (
              <TaskCard
                key={task.id}
                task={task}
                onPress={() => router.push(`/task/${task.id}`)}
              />
            ))
          }
        </View>
      )}

      {fields.length > 0 && (
        <View style={styles.recentFieldsContainer}>
          <View style={[styles.sectionHeader, , isRTL && { flexDirection: 'row-reverse' }]}>
            <Text style={styles.sectionTitle}>{t('entity.farm.fields')}</Text>
            <TouchableOpacity onPress={() => setActiveTab('fields')}>
              <Text style={styles.seeAllText}>{t('entity.farm.viewAllFields')}</Text>
            </TouchableOpacity>
          </View>

          <ScrollView
            horizontal
            style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={[styles.horizontalScrollContent]}
          >
            {fields.map(field => (
              <TouchableOpacity
                key={field.id}
                style={styles.fieldCard}
                onPress={() => router.push(`/field/${field.id}`)}
              >
                <Image
                  source={{ uri: field.image || 'https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8ZmllbGR8ZW58MHx8MHx8&w=1000&q=80' }}
                  style={styles.fieldCardImage}
                />
                <View style={styles.fieldCardOverlay}>
                  <Text style={styles.fieldCardTitle}>{field.name}</Text>
                  <Text style={styles.fieldCardSubtitle}>
                    {field.size} {field.sizeUnit} • {field.type}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}

      {gardens.length > 0 && (
        <View style={styles.recentFieldsContainer}>
          <View style={[styles.sectionHeader, isRTL && { flexDirection: 'row-reverse' }]}>
            <Text style={styles.sectionTitle}>{t('entity.farm.Gardens')}</Text>
            <TouchableOpacity onPress={() => setActiveTab('gardens')}>
              <Text style={styles.seeAllText}>{t('entity.farm.viewAllGardens')}</Text>
            </TouchableOpacity>
          </View>

          <ScrollView
            horizontal
            style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={[styles.horizontalScrollContent, isRTL && styles.rowReverse]}
          >
            {gardens.map(garden => (
              <TouchableOpacity
                key={garden.id}
                style={styles.fieldCard}
                onPress={() => router.push(`/garden/${garden.id}`)}
              >
                <Image
                  source={{ uri: garden.image || 'https://images.unsplash.com/photo-1585320806297-9794b3e4eeae?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8M3x8Z2FyZGVufGVufDB8fDB8fA%3D%3D&w=1000&q=80' }}
                  style={styles.fieldCardImage}
                />
                <View style={styles.fieldCardOverlay}>
                  <Text style={styles.fieldCardTitle}>{garden.name}</Text>
                  <Text style={styles.fieldCardSubtitle}>
                    {garden.size} {garden.sizeUnit} • {garden.type}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}

      <View style={styles.teamSection}>
        <View style={[styles.sectionHeader, isRTL && { flexDirection: 'row-reverse' }]}>
          <Text style={styles.sectionTitle}>{t('entity.farm.team')}</Text>
          <TouchableOpacity onPress={() => setActiveTab('team')}>
            <Text style={styles.seeAllText}>{t('entity.farm.viewAll')}</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.teamList}>
          {loadingFarmUsers ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color={colors.primary} />
              <Text style={styles.loadingText}>{t('entity.farm.loadingTeam')}...</Text>
            </View>
          ) : farmUsers.length > 0 ? (
            farmUsers.slice(0, 3).map(member => (
              // <View key={member.id} style={[styles.teamMember, isRTL && { flexDirection: 'row-reverse' }]}>
              //   <Image
              //     source={{ uri: member.avatar || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80' }}
              //     style={styles.teamMemberAvatar}
              //   />
              //   <View style={[styles.teamMemberInfo]}>
              //     <Text style={[styles.teamMemberName, isRTL && { textAlign: 'right', marginRight: 8 }]}>{member.displayName || member?.name}</Text>
              //     <Text style={[styles.teamMemberRole, isRTL && { textAlign: 'right', marginRight: 8 }]}>
              //       {member.role.charAt(0).toUpperCase() + member.role.slice(1)}
              //     </Text>
              //   </View>
              // </View>
              <TeamMemberCard
                key={member.id}
                item={member}
                userRole={user?.role}
                onRemoveUser={handleRemoveUser}
              />
            ))
          ) : (
            <View style={styles.emptyTeamContainer}>
              <Text style={styles.emptyTeamText}>{t('entity.farm.noTeamMembers')}</Text>
            </View>
          )}

          {farmUsers.length > 3 && (
            <TouchableOpacity
              style={styles.viewMoreButton}
              onPress={() => setActiveTab('team')}
            >
              <Text style={styles.viewMoreText}>+{farmUsers.length - 3} {t('entity.farm.more')}</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </ScrollView>
  );

  const renderFieldsTab = () => (
    <View style={styles.tabContent}>
      <View style={[styles.listHeader, isRTL && { flexDirection: 'row-reverse' }]}>
        <Text style={styles.listTitle}>{t('entity.farm.fields')} ({fields.length})</Text>
        <TouchableOpacity
          style={[styles.addButton, isRTL && { flexDirection: 'row-reverse' }]}
          onPress={() => router.push('/field/create')}
        >
          <Plus size={20} color={colors.white} />
          <Text style={styles.addButtonText}>{t('entity.farm.addField')}</Text>
        </TouchableOpacity>
      </View>

      {fields.length === 0 ? (
        <View style={styles.emptyState}>
          <Leaf size={40} color={colors.gray[400]} />
          <Text style={styles.emptyStateText}>{t('entity.farm.noFieldsAdded')}</Text>
          <TouchableOpacity
            style={styles.emptyStateButton}
            onPress={() => router.push('/field/create')}
          >
            <Text style={styles.emptyStateButtonText}>{t('add.field')}</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <FlatList
          data={fields}
          keyExtractor={item => item.id}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={[styles.listItem, isRTL && { flexDirection: 'row-reverse' }]}
              onPress={() => router.push(`/field/${item.id}`)}
            >
              <Image
                source={{ uri: item.image || 'https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8ZmllbGR8ZW58MHx8MHx8&w=1000&q=80' }}
                style={styles.listItemImage}
              />
              <View style={[styles.listItemContent]}>
                <Text style={[styles.listItemTitle, isRTL && { textAlign: 'right', marginRight: 8 }]}>{item.name}</Text>
                <Text style={[styles.listItemSubtitle, isRTL && { textAlign: 'right', marginRight: 8 }]}>
                  {item.size?.toFixed(2)} {t(`common.areaUnit.${item.sizeUnit}`)} • {t(`entity.field.type${item.type}`)}
                </Text>
                {item?.cropType && (
                  <Text style={styles.listItemDetail}>{t('entity.farm.cropType')}: {item.cropType}</Text>
                )}
                <View style={[styles.listItemStatus, isRTL && { flexDirection: 'row-reverse' }]}>
                  <View style={[styles.statusIndicator, { backgroundColor: getStatusColor(item.status) }]} />
                  <Text style={[styles.statusText, isRTL && { marginRight: 8 }]}>{t(`entity.field.${item.status}`)}</Text>
                </View>
              </View>
              {isRTL ? <ChevronLeft size={20} color={colors.gray[400]} /> : <ChevronRight size={20} color={colors.gray[400]} />}
              {/* <ChevronRight size={20} color={colors.gray[400]} /> */}
            </TouchableOpacity>
          )}
          contentContainerStyle={styles.listContent}
        />
      )}
    </View>
  );

  const renderGardensTab = () => (
    <View style={styles.tabContent}>
      <View style={[styles.listHeader, isRTL && { flexDirection: 'row-reverse' }]}>
        <Text style={styles.listTitle}>{t('entity.farm.Gardens')} ({gardens.length})</Text>
        <TouchableOpacity
          style={[styles.addButton, isRTL && { flexDirection: 'row-reverse' }]}
          onPress={() => router.push('/garden/create')}
        >
          <Plus size={20} color={colors.white} />
          <Text style={styles.addButtonText}>{t('entity.farm.addGarden')}</Text>
        </TouchableOpacity>
      </View>

      {gardens.length === 0 ? (
        <View style={styles.emptyState}>
          <TreeDeciduous size={40} color={colors.gray[400]} />
          <Text style={styles.emptyStateText}>{t('entity.farm.noGardensAdded')}</Text>
          <TouchableOpacity
            style={styles.emptyStateButton}
            onPress={() => router.push('/garden/create')}
          >
            <Text style={styles.emptyStateButtonText}>{t('entity.farm.addGarden')}</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <FlatList
          data={gardens}
          keyExtractor={item => item.id}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={[styles.listItem, isRTL && { flexDirection: 'row-reverse' }]}
              onPress={() => router.push(`/garden/${item.id}`)}
            >
              <Image
                source={{ uri: item.image || 'https://images.unsplash.com/photo-1585320806297-9794b3e4eeae?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8M3x8Z2FyZGVufGVufDB8fDB8fA%3D%3D&w=1000&q=80' }}
                style={styles.listItemImage}
              />
              <View style={styles.listItemContent}>
                <Text style={[styles.listItemTitle, isRTL && { textAlign: 'right', marginRight: 8 }]}>{item.name}</Text>
                <Text style={[styles.listItemSubtitle, isRTL && { textAlign: 'right', marginRight: 8 }]}>
                  {item.size?.toFixed(2)} {t(`common.areaUnit.${item.sizeUnit}`)} • {t(`entity.garden.type${item.gardenType}`)}
                </Text>
                {item.soilType && (
                  <Text style={[styles.listItemDetail, isRTL && { textAlign: 'right', marginRight: 8 }]}>{t('entity.garden.selectSoilType')}: {t(`entity.garden.soilType${item.soilType}`)}</Text>
                )}
                <View style={[styles.listItemStatus, isRTL && { flexDirection: 'row-reverse' }]}>
                  <View style={[styles.statusIndicator, { backgroundColor: getStatusColor(item.status) }]} />
                  <Text style={[styles.statusText, isRTL && { textAlign: 'right', marginRight: 8 }]}>{item.status}</Text>
                </View>
              </View>
              {isRTL ? <ChevronLeft size={20} color={colors.gray[400]} /> : <ChevronRight size={20} color={colors.gray[400]} />}
              {/* <ChevronRight size={20} color={colors.gray[400]} /> */}
            </TouchableOpacity>
          )}
          contentContainerStyle={styles.listContent}
        />
      )}
    </View>
  );

  const renderAnimalsTab = () => (
    <View style={styles.tabContent}>
      <View style={[styles.listHeader, isRTL && { flexDirection: 'row-reverse' }]}>
        <Text style={styles.listTitle}>{t('entity.farm.Animals')} ({animals.length})</Text>
        <TouchableOpacity
          style={[styles.addButton, isRTL && { flexDirection: 'row-reverse' }]}
          onPress={() => router.push('/animal/create')}
        >
          <Plus size={20} color={colors.white} />
          <Text style={styles.addButtonText}>{t('entity.farm.addAnimal')}</Text>
        </TouchableOpacity>
      </View>

      {animals.length === 0 ? (
        <View style={styles.emptyState}>
          <Rabbit size={40} color={colors.gray[400]} />
          <Text style={styles.emptyStateText}>{t('entity.farm.noAnimalsAdded')}</Text>
          <TouchableOpacity
            style={styles.emptyStateButton}
            onPress={() => router.push('/animal/create')}
          >
            <Text style={styles.emptyStateButtonText}>{t('entity.farm.addAnimal')}</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <FlatList
          data={animals}
          keyExtractor={item => item.id}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={[styles.listItem, isRTL && { flexDirection: 'row-reverse' }]}
              onPress={() => router.push(`/animal/${item.id}`)}
            >
              <Image
                source={{ uri: item.image || 'https://images.unsplash.com/photo-1570042225831-d98fa7577f1e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8NHx8Y293fGVufDB8fDB8fA%3D%3D&w=1000&q=80' }}
                style={styles.listItemImage}
              />
              <View style={styles.listItemContent}>
                <Text style={[styles.listItemTitle, isRTL && { textAlign: 'right', marginRight: 8 }]}>{item.name || item.species}</Text>
                <Text style={[styles.listItemSubtitle, isRTL && { textAlign: 'right', marginRight: 8 }]}>
                  {t(`form.${item.species}`)} {item.breed ? `• ${t(`form.${item.breed}`)}` : ''}
                </Text>
                {/* <Text style={[styles.listItemDetail,isRTL && {textAlign:'right',marginRight:8}]}>{t('entity.animal.purpose')}: {item.purpose}</Text> */}
                {/* <View style={[styles.listItemStatus, isRTL && { flexDirection: 'row-reverse' }]}>
                  <View style={[styles.statusIndicator, { backgroundColor: getAnimalStatusColor(item.status) }]} />
                  <Text style={[styles.statusText, isRTL && { textAlign: 'right', marginRight: 8 }]}>{t(`form.${item.status}`)}</Text>
                </View> */}
              </View>
              {isRTL ? <ChevronLeft size={20} color={colors.gray[400]} /> : <ChevronRight size={20} color={colors.gray[400]} />}
              {/* <ChevronRight size={20} color={colors.gray[400]} /> */}
            </TouchableOpacity>
          )}
          contentContainerStyle={styles.listContent}
        />
      )}
    </View>
  );

  const renderEquipmentTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.listHeader}>
        <Text style={styles.listTitle}>{t('entity.farm.Equipment')} ({inventoryEquipment ? inventoryEquipment.length : 0})</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => router.push('/equipment/' as any)}
        >
          <Plus size={20} color={colors.white} />
          <Text style={styles.addButtonText}>{t('entity.farm.addEquipment')}</Text>
        </TouchableOpacity>
      </View>

      {!inventoryEquipment || inventoryEquipment.length === 0 ? (
        <View style={styles.emptyState}>
          <Tractor size={40} color={colors.gray[400]} />
          <Text style={styles.emptyStateText}>{t('entity.farm.noEquipmentAdded')}</Text>
          <TouchableOpacity
            style={styles.emptyStateButton}
            onPress={() => router.push('/equipment/' as any)}
          >
            <Text style={styles.emptyStateButtonText}>{t('entity.farm.addEquipment')}</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <FlatList
          data={inventoryEquipment}
          keyExtractor={item => item.id}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={[styles.listItem, isRTL && { flexDirection: 'row-reverse' }]}
              onPress={() => router.push(`/equipment/${item.id}`)}
            >
              <Image
                source={{ uri: item.imageUrl || 'https://images.unsplash.com/photo-1605000797499-95a51c5269ae?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8dHJhY3RvcnxlbnwwfHwwfHw%3D&w=1000&q=80' }}
                style={styles.listItemImage}
              />
              <View style={styles.listItemContent}>
                <Text
                  style={[
                    styles.listItemTitle,
                    isRTL && { textAlign: 'right', marginRight: 8 },
                  ]}
                >
                  {item.name}
                </Text>
                <View style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}>
                  <Text style={[styles.listItemSubtitle, isRTL && { textAlign: 'right', marginRight: 8 }]}>
                    {item.category}
                    {item.supplier ? ` • ${item.supplier}` : ''}
                  </Text>
                  <Text> | </Text>
                  <Text style={[styles.listItemDetail, isRTL && { textAlign: 'right', marginRight: 8 }]}>
                    {item.quantity} {item.unit}
                  </Text>
                </View>
                <View style={[styles.listItemStatus, isRTL && { flexDirection: 'row-reverse', marginRight: 8 }]}>
                  <View
                    style={[
                      styles.statusIndicator,
                      { backgroundColor: getInventoryEquipmentStatusColor(item) },
                    ]}
                  />
                  <Text style={[styles.statusText, isRTL && { marginRight: 8 }]}>
                    {getInventoryEquipmentStatusText(item)}
                  </Text>
                </View>
              </View>

              {/* <View style={styles.listItemContent}>
                <Text style={[styles.listItemTitle,isRTL && {textAlign:'right',marginRight:8}]}>{item.name}</Text>
                <Text style={styles.listItemSubtitle}>
                  {item.type} {item.model ? `• ${item.model}` : ''}
                </Text>
                {item.manufacturer && (
                  <Text style={styles.listItemDetail}>Make: {item.manufacturer}</Text>
                )}
                <View style={styles.listItemStatus}>
                  <View style={[styles.statusIndicator, { backgroundColor: getEquipmentStatusColor(item.status) }]} />
                  <Text style={styles.statusText}>{item.status}</Text>
                </View>
              </View> */}
              <ChevronRight size={20} color={colors.gray[400]} />
            </TouchableOpacity>
          )}
          contentContainerStyle={styles.listContent}
        />
      )}
    </View>
  );

  const renderTasksTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.listHeader}>
        <Text style={styles.listTitle}>{t('entity.farm.Tasks')} ({tasks.length})</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => router.push('/task/create')}
        >
          <Plus size={20} color={colors.white} />
          <Text style={styles.addButtonText}>{t('entity.farm.addTask')}</Text>
        </TouchableOpacity>
      </View>

      {tasks.length === 0 ? (
        <View style={styles.emptyState}>
          <Calendar size={40} color={colors.gray[400]} />
          <Text style={styles.emptyStateText}>{t('entity.farm.noTasksAdded')}</Text>
          <TouchableOpacity
            style={styles.emptyStateButton}
            onPress={() => router.push('/task/create')}
          >
            <Text style={styles.emptyStateButtonText}>{t('entity.farm.addTask')}</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <FlatList
          data={tasks.sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime())}
          keyExtractor={item => item.id}
          renderItem={({ item }) => (
            <TaskCard
              task={item}
              onPress={() => router.push(`/task/${item.id}`)}
            />
          )}
          contentContainerStyle={styles.listContent}
        />
      )}
    </View>
  );

  const renderTeamTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.listHeader}>
        <Text style={styles.listTitle}>Team Members ({farmUsers.length})</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => router.push('/user/invite')}
        >
          <UserPlus size={20} color={colors.white} />
          <Text style={styles.addButtonText}>Invite</Text>
        </TouchableOpacity>
      </View>

      {loadingFarmUsers ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Loading team members...</Text>
        </View>
      ) : farmUsers.length > 0 ? (
        <FlatList
          data={farmUsers}
          keyExtractor={item => item.id}
          renderItem={({ item }) => (
            <TeamMemberCard
              item={item}
              userRole={user?.role}
              onRemoveUser={handleRemoveUser}
            />
          )}
          contentContainerStyle={styles.listContent}
        />
      ) : (
        <View style={styles.emptyState}>
          <Users size={40} color={colors.gray[400]} />
          <Text style={styles.emptyStateText}>No team members found</Text>
          <TouchableOpacity
            style={styles.emptyStateButton}
            onPress={() => router.push('/user/invite')}
          >
            <Text style={styles.emptyStateButtonText}>Invite Users</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  const handleRemoveUser = (userId: string) => {
    Alert.alert(
      'Confirm Removal',
      'Are you sure you want to remove this user from the farm?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              // In a real app, implement user removal logic
              Alert.alert('Success', 'User removed successfully');

              // Refresh farm users list
              if (farm?.id) {
                loadFarmUsers(farm.id);
              }
            } catch (error: any) {
              console.error('Remove user error:', error);
              Alert.alert('Error', error.message || 'Failed to remove user');
            }
          }
        }
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen
        options={{
          title: farm.name,
          // headerRight: () => (
          //   <TouchableOpacity
          //     style={styles.headerButton}
          //     onPress={() => setShowOptions(!showOptions)}
          //   >
          //     <MoreHorizontal size={24} color={colors.gray[700]} />
          //   </TouchableOpacity>
          // ),
        }}
      />

      {/* {showOptions && (
        <View style={styles.optionsMenu}>
          <TouchableOpacity
            style={styles.optionItem}
            onPress={() => {
              setShowOptions(false);
              setShowEditModal(true);
            }}
          >
            <Edit size={20} color={colors.gray[700]} />
            <Text style={styles.optionText}>Edit Farm</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.optionItem}
            onPress={() => {
              setShowOptions(false);
              router.push('/user/invite');
            }}
          >
            <UserPlus size={20} color={colors.gray[700]} />
            <Text style={styles.optionText}>Invite User</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.optionItem}
            onPress={() => {
              setShowOptions(false);
              setShowDeleteConfirm(true);
            }}
          >
            <Trash2 size={20} color={colors.danger} />
            <Text style={[styles.optionText, { color: colors.danger }]}>Delete Farm</Text>
          </TouchableOpacity>
        </View>
      )} */}

      <View style={styles.farmHeader}>
        <View style={[styles.farmInfo, isRTL && styles.rowReverse]}>
          <View style={[styles.farmLocation, isRTL && styles.rowReverse]}>
            <MaterialIcons
              name="location-on"
              size={16}
              color={colors.gray[500]}
              style={isRTL && { marginLeft: 6, marginRight: 0 }}
            />
            <Text style={styles.farmLocationText}>
              {farm.location || t('farm.noLocation')}
            </Text>
          </View>

          <View style={styles.farmSize}>
            {/* <Text style={styles.farmSizeText}>
              {farm.size && farm.sizeUnit
                ? t('farm.', { size: farm.size, unit: farm.sizeUnit })
                : t('farm.noSize')}
            </Text> */}
            <Text style={styles.farmSizeText}>
              {farm.size && farm.sizeUnit
                ? t('farm.sizeLabel', { size: farm.size, unit: farm.sizeUnit })
                : t('farm.noSize')}
            </Text>
          </View>
        </View>

        {farm.description && (
          <Text style={[styles.farmDescription, isRTL && styles.rtlTextAlign]}>{farm.description}</Text>
        )}
      </View>

      <View style={[styles.tabsContainer]}>
        <ScrollView
          horizontal
          // alwaysBounceHorizontal
          // style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={[styles.tabs, isRTL && styles.rowReverse]}
        >
          <TouchableOpacity
            style={[styles.tab, activeTab === 'overview' && styles.activeTab]}
            onPress={() => setActiveTab('overview')}
          >
            <Text style={[styles.tabText, activeTab === 'overview' && styles.activeTabText, isRTL && styles.rtlTextAlign]}>{t('entity.farm.overview')}</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'fields' && styles.activeTab]}
            onPress={() => setActiveTab('fields')}
          >
            <Text style={[styles.tabText, activeTab === 'fields' && styles.activeTabText, isRTL && styles.rtlTextAlign]}>{t('entity.farm.Fields')}</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'gardens' && styles.activeTab]}
            onPress={() => setActiveTab('gardens')}
          >
            <Text style={[styles.tabText, activeTab === 'gardens' && styles.activeTabText, isRTL && styles.rtlTextAlign]}>{t('entity.farm.Gardens')}</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'animals' && styles.activeTab]}
            onPress={() => setActiveTab('animals')}
          >
            <Text style={[styles.tabText, activeTab === 'animals' && styles.activeTabText, isRTL && styles.rtlTextAlign]}>{t('entity.farm.Animals')}</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'equipment' && styles.activeTab, isRTL && styles.rtlTextAlign]}
            onPress={() => setActiveTab('equipment')}
          >
            <Text style={[styles.tabText, activeTab === 'equipment' && styles.activeTabText]}>{t('entity.farm.Equipment')}</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'tasks' && styles.activeTab]}
            onPress={() => setActiveTab('tasks')}
          >
            <Text style={[styles.tabText, activeTab === 'tasks' && styles.activeTabText]}>{t('entity.farm.Tasks')}</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'team' && styles.activeTab]}
            onPress={() => setActiveTab('team')}
          >
            <Text style={[styles.tabText, activeTab === 'team' && styles.activeTabText]}>{t('entity.farm.Team')}</Text>
          </TouchableOpacity>
        </ScrollView>
      </View>

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      ) : (
        <>
          {activeTab === 'overview' && renderOverviewTab()}
          {activeTab === 'fields' && renderFieldsTab()}
          {activeTab === 'gardens' && renderGardensTab()}
          {activeTab === 'animals' && renderAnimalsTab()}
          {activeTab === 'equipment' && renderEquipmentTab()}
          {activeTab === 'tasks' && renderTasksTab()}
          {activeTab === 'team' && renderTeamTab()}
        </>
      )}

      {/* Edit Farm Modal */}
      <Modal
        visible={showEditModal}
        animationType="slide"
        onRequestClose={() => setShowEditModal(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              onPress={() => setShowEditModal(false)}
              style={styles.modalCloseButton}
            >
              <X size={24} color={colors.gray[700]} />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Edit Farm</Text>
            <TouchableOpacity
              onPress={handleEditFarm}
              style={styles.modalSaveButton}
            >
              <Text style={styles.modalSaveText}>Save</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Farm Name *</Text>
              <TextInput
                style={styles.formInput}
                value={editName}
                onChangeText={setEditName}
                placeholder="Enter farm name"
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Location</Text>
              <TextInput
                style={styles.formInput}
                value={editLocation}
                onChangeText={setEditLocation}
                placeholder="Enter location"
              />
            </View>

            <View style={styles.formRow}>
              <View style={[styles.formGroup, { flex: 2, marginRight: 8 }]}>
                <Text style={styles.formLabel}>Size</Text>
                <TextInput
                  style={styles.formInput}
                  value={editSize}
                  onChangeText={setEditSize}
                  placeholder="Enter size"
                  keyboardType="numeric"
                />
              </View>

              <View style={[styles.formGroup, { flex: 1 }]}>
                <Text style={styles.formLabel}>Unit</Text>
                <TouchableOpacity
                  style={styles.formSelect}
                  onPress={() => {
                    setEditSizeUnit(editSizeUnit === 'acres' ? 'hectares' : 'acres');
                  }}
                >
                  <Text style={styles.formSelectText}>{editSizeUnit}</Text>
                  <ChevronDown size={20} color={colors.gray[500]} />
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Farm Type</Text>
              <View style={styles.typeButtons}>
                <TouchableOpacity
                  style={[
                    styles.typeButton,
                    editType === 'crop' && styles.typeButtonActive
                  ]}
                  onPress={() => setEditType('crop')}
                >
                  <Text style={[
                    styles.typeButtonText,
                    editType === 'crop' && styles.typeButtonTextActive
                  ]}>Crop</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.typeButton,
                    editType === 'livestock' && styles.typeButtonActive
                  ]}
                  onPress={() => setEditType('livestock')}
                >
                  <Text style={[
                    styles.typeButtonText,
                    editType === 'livestock' && styles.typeButtonTextActive
                  ]}>Livestock</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.typeButton,
                    editType === 'mixed' && styles.typeButtonActive
                  ]}
                  onPress={() => setEditType('mixed')}
                >
                  <Text style={[
                    styles.typeButtonText,
                    editType === 'mixed' && styles.typeButtonTextActive
                  ]}>Mixed</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.typeButton,
                    editType === 'orchard' && styles.typeButtonActive
                  ]}
                  onPress={() => setEditType('orchard')}
                >
                  <Text style={[
                    styles.typeButtonText,
                    editType === 'orchard' && styles.typeButtonTextActive
                  ]}>Orchard</Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Description</Text>
              <TextInput
                style={[styles.formInput, styles.formTextarea]}
                value={editDescription}
                onChangeText={setEditDescription}
                placeholder="Enter farm description"
                multiline
                numberOfLines={4}
              />
            </View>
          </ScrollView>
        </SafeAreaView>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        visible={showDeleteConfirm}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowDeleteConfirm(false)}
      >
        <View style={styles.confirmModalOverlay}>
          <View style={styles.confirmModalContent}>
            <Text style={styles.confirmModalTitle}>Delete Farm</Text>
            <Text style={styles.confirmModalText}>
              Are you sure you want to delete this farm? This action cannot be undone and will delete all associated data.
            </Text>

            <View style={styles.confirmModalButtons}>
              <TouchableOpacity
                style={[styles.confirmModalButton, styles.confirmModalCancelButton]}
                onPress={() => setShowDeleteConfirm(false)}
              >
                <Text style={styles.confirmModalCancelText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.confirmModalButton, styles.confirmModalDeleteButton]}
                onPress={handleDeleteFarm}
              >
                <Text style={styles.confirmModalDeleteText}>Delete</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Invite User Modal */}
      <Modal
        visible={showInviteModal}
        animationType="slide"
        onRequestClose={() => setShowInviteModal(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              onPress={() => setShowInviteModal(false)}
              style={styles.modalCloseButton}
            >
              <X size={24} color={colors.gray[700]} />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Invite User</Text>
            <View style={styles.modalHeaderRight} />
          </View>

          <View style={styles.modalContent}>
            <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Email Address *</Text>
              <TextInput
                style={styles.formInput}
                value={inviteEmail}
                onChangeText={setInviteEmail}
                placeholder="Enter email address"
                keyboardType="email-address"
                autoCapitalize="none"
              />
            </View>

            <View style={styles.formGroup}>
              <Text style={styles.formLabel}>Role</Text>
              <View style={styles.roleButtons}>
                <TouchableOpacity
                  style={[
                    styles.roleButton,
                    inviteRole === 'admin' && styles.roleButtonActive
                  ]}
                  onPress={() => setInviteRole('admin')}
                >
                  <Text style={[
                    styles.roleButtonText,
                    inviteRole === 'admin' && styles.roleButtonTextActive
                  ]}>Manager</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.roleButton,
                    inviteRole === 'caretaker' && styles.roleButtonActive
                  ]}
                  onPress={() => setInviteRole('caretaker')}
                >
                  <Text style={[
                    styles.roleButtonText,
                    inviteRole === 'caretaker' && styles.roleButtonTextActive
                  ]}>Caretaker</Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.roleDescription}>
              {inviteRole === 'admin' ? (
                <Text style={styles.roleDescriptionText}>
                  Managers can create and assign tasks, manage all farm entities, and view reports.
                </Text>
              ) : (
                <Text style={styles.roleDescriptionText}>
                  Caretakers can view and complete assigned tasks, and update the status of farm entities.
                </Text>
              )}
            </View>

            <Button
              title="Send Invitation"
              onPress={handleInviteUser}
              style={styles.inviteButton}
            />
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

// Helper functions for status colors
const getStatusColor = (status: string) => {
  switch (status) {
    case 'active':
      return colors.success;
    case 'fallow':
      return colors.warning;
    case 'maintenance':
      return colors.info;
    case 'inactive':
      return colors.gray[400];
    case 'planning':
      return colors.secondary;
    default:
      return colors.gray[500];
  }
};

const getAnimalStatusColor = (status: string) => {
  switch (status) {
    case 'healthy':
      return colors.success;
    case 'sick':
      return colors.danger;
    case 'pregnant':
      return colors.info;
    case 'nursing':
      return colors.warning;
    case 'quarantined':
      return colors.secondary;
    default:
      return colors.gray[500];
  }
};

const getEquipmentStatusColor = (status: string) => {
  switch (status) {
    case 'operational':
      return colors.success;
    case 'maintenance':
      return colors.warning;
    case 'repair':
      return colors.danger;
    case 'retired':
      return colors.gray[500];
    default:
      return colors.gray[500];
  }
};

// Inventory equipment status functions
const getInventoryEquipmentStatusColor = (item: any) => {
  if (!item) return colors.gray[500];

  if (item.quantity <= (item.minQuantity || 0)) {
    return colors.danger;
  }
  if (item.expiryDate && new Date(item.expiryDate) < new Date()) {
    return colors.warning;
  }
  return colors.success;
};

const getInventoryEquipmentStatusText = (item: any) => {
  if (!item) return '';

  if (item.quantity <= (item.minQuantity || 0)) {
    return 'Low Stock';
  }
  if (item.expiryDate && new Date(item.expiryDate) < new Date()) {
    return 'Expired';
  }
  return 'Available';
};

const getTaskStatusColor = (status: string) => {
  switch (status) {
    case 'completed':
      return colors.success;
    case 'in_progress':
      return colors.info;
    case 'pending':
      return colors.warning;
    case 'overdue':
      return colors.danger;
    default:
      return colors.gray[500];
  }
};

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'low':
      return colors.info;
    case 'medium':
      return colors.warning;
    case 'high':
      return colors.danger;
    case 'urgent':
      return colors.danger;
    default:
      return colors.gray[500];
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  rowReverse: {
    flexDirection: 'row-reverse',
  },
  rtlTextAlign: {
    textAlign: 'right',
    marginRight: 8
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
    color: colors.gray[600],
  },
  headerButton: {
    padding: 8,
  },
  optionsMenu: {
    position: 'absolute',
    top: 50,
    right: 16,
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: 8,
    zIndex: 10,
    shadowColor: colors.gray[800],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  optionText: {
    fontSize: 14,
    color: colors.gray[800],
    marginLeft: 8,
  },
  farmHeader: {
    backgroundColor: colors.white,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  farmInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  farmLocation: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  farmLocationText: {
    fontSize: 14,
    color: colors.gray[600],
    marginLeft: 4,
  },
  farmSize: {
    backgroundColor: colors.gray[100],
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  farmSizeText: {
    fontSize: 12,
    color: colors.gray[700],
    fontWeight: '500',
  },
  farmDescription: {
    fontSize: 14,
    color: colors.gray[600],
    lineHeight: 20,
  },
  tabsContainer: {
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  tabs: {
    paddingHorizontal: 16,
  },
  tab: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginRight: 8,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
  },
  tabText: {
    fontSize: 14,
    color: colors.gray[600],
    fontWeight: '500',
  },
  activeTabText: {
    color: colors.primary,
    fontWeight: '600',
  },
  tabContent: {
    flex: 1,
    padding: 16,
  },
  overviewScrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  statCard: {
    width: (width - 48) / 2,
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    alignItems: 'center',
  },
  statIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.gray[900],
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    color: colors.gray[600],
  },
  quickActionsContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 16,
  },
  quickActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionButton: {
    width: '48%',
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  quickActionIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  quickActionText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[800],
  },
  upcomingTasksContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  seeAllText: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '500',
  },
  recentFieldsContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  horizontalScrollContent: {
    paddingRight: 16,
  },
  fieldCard: {
    width: 200,
    height: 120,
    borderRadius: 8,
    marginRight: 12,
    overflow: 'hidden',
  },
  fieldCardImage: {
    width: '100%',
    height: '100%',
  },
  fieldCardOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 8,
  },
  fieldCardTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.white,
    marginBottom: 2,
  },
  fieldCardSubtitle: {
    fontSize: 12,
    color: colors.gray[200],
  },
  teamSection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  teamList: {
    marginBottom: 8,
  },
  teamMember: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  teamMemberAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  teamMemberInfo: {
    flex: 1,
  },
  teamMemberName: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[800],
    marginBottom: 2,
  },
  teamMemberRole: {
    fontSize: 12,
    color: colors.gray[600],
  },
  viewMoreButton: {
    alignItems: 'center',
    paddingVertical: 8,
  },
  viewMoreText: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '500',
  },
  listHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  listTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  addButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
    marginLeft: 4,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyStateText: {
    fontSize: 16,
    color: colors.gray[600],
    marginTop: 12,
    marginBottom: 24,
  },
  emptyStateButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
  },
  emptyStateButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
  },
  listContent: {
    paddingBottom: 16,
  },
  listItem: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 12,
    marginBottom: 12,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    alignItems: 'center',
  },
  listItemImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 12,
  },
  listItemContent: {
    flex: 1,
  },
  listItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 4,
  },
  listItemSubtitle: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 4,
  },
  listItemDetail: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 4,
  },
  listItemStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 4,
  },
  statusText: {
    fontSize: 12,
    color: colors.gray[600],
    textTransform: 'capitalize',
  },
  teamMemberCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 12,
    marginBottom: 12,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  teamMemberCardAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  teamMemberCardContent: {
    flex: 1,
  },
  teamMemberCardName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 2,
  },
  teamMemberCardEmail: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 2,
  },
  teamMemberCardPhone: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 4,
  },
  roleBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  roleText: {
    fontSize: 12,
    fontWeight: '500',
  },
  removeButton: {
    padding: 8,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: colors.white,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalCloseButton: {
    padding: 4,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
  },
  modalSaveButton: {
    padding: 4,
  },
  modalSaveText: {
    fontSize: 16,
    color: colors.primary,
    fontWeight: '500',
  },
  modalHeaderRight: {
    width: 32,
  },
  modalContent: {
    padding: 16,
    flex: 1,
  },
  formGroup: {
    marginBottom: 16,
  },
  formLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 8,
  },
  formInput: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: colors.gray[800],
  },
  formTextarea: {
    height: 100,
    textAlignVertical: 'top',
  },
  formRow: {
    flexDirection: 'row',
  },
  formSelect: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  formSelectText: {
    fontSize: 16,
    color: colors.gray[800],
  },
  typeButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  typeButton: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    marginBottom: 8,
  },
  typeButtonActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  typeButtonText: {
    fontSize: 14,
    color: colors.gray[800],
  },
  typeButtonTextActive: {
    color: colors.white,
    fontWeight: '500',
  },
  confirmModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  confirmModalContent: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 20,
    width: '80%',
    maxWidth: 400,
  },
  confirmModalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 12,
  },
  confirmModalText: {
    fontSize: 14,
    color: colors.gray[700],
    marginBottom: 20,
    lineHeight: 20,
  },
  confirmModalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  confirmModalButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginLeft: 12,
  },
  confirmModalCancelButton: {
    backgroundColor: colors.gray[200],
  },
  confirmModalCancelText: {
    color: colors.gray[800],
    fontWeight: '500',
  },
  confirmModalDeleteButton: {
    backgroundColor: colors.danger,
  },
  confirmModalDeleteText: {
    color: colors.white,
    fontWeight: '500',
  },
  roleButtons: {
    flexDirection: 'row',
  },
  roleButton: {
    flex: 1,
    borderWidth: 1,
    borderColor: colors.gray[300],
    paddingVertical: 10,
    alignItems: 'center',
  },
  roleButtonActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  roleButtonText: {
    fontSize: 14,
    color: colors.gray[800],
  },
  roleButtonTextActive: {
    color: colors.white,
    fontWeight: '500',
  },
  roleDescription: {
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    padding: 12,
    marginBottom: 24,
  },
  roleDescriptionText: {
    fontSize: 14,
    color: colors.gray[700],
    lineHeight: 20,
  },
  inviteButton: {
    marginTop: 16,
  },
  emptyTeamContainer: {
    padding: 16,
    alignItems: 'center',
    backgroundColor: colors.gray[50],
    borderRadius: 8,
  },
  emptyTeamText: {
    fontSize: 14,
    color: colors.gray[600],
  },
});
