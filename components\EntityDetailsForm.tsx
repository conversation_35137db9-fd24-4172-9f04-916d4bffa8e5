import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
// import { Picker } from '@react-native-picker/picker';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Ionicons } from '@expo/vector-icons';
import { SuggestedAction } from '@/services/ai-assistant';
import { colors } from '@/constants/colors';
import { StyleSheet } from 'react-native';
import DropdownPicker from '@/components/DropdownPicker';

interface EntityDetailsFormProps {
  action: SuggestedAction;
  onSave: (updatedData: any) => void;
  onCancel: () => void;
}

export default function EntityDetailsForm({
  action,
  onSave,
  onCancel,
}: EntityDetailsFormProps) {
  const [formData, setFormData] = useState(action.data);
  const [showDatePicker, setShowDatePicker] = useState<string | null>(null);

  const updateField = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const renderAnimalForm = () => (
    <>
      <Text style={styles.sectionTitle}>Animal Details</Text>
      
      <View style={styles.fieldGroup}>
        <Text style={styles.label}>Species *</Text>
        <TextInput
          style={styles.input}
          value={formData.species || ''}
          onChangeText={(value) => updateField('species', value)}
          placeholder="e.g., Cattle, Sheep, Goat"
        />
      </View>

      <View style={styles.fieldGroup}>
        <Text style={styles.label}>Breed</Text>
        <TextInput
          style={styles.input}
          value={formData.breed || ''}
          onChangeText={(value) => updateField('breed', value)}
          placeholder="e.g., Holstein, Angus"
        />
      </View>

      <View style={styles.fieldGroup}>
        <Text style={styles.label}>Gender</Text>
        <View style={styles.pickerContainer}>
          {/* <Picker
            selectedValue={formData.gender || 'unknown'}
            onValueChange={(value) => updateField('gender', value)}
            style={styles.picker}
          >
            <Picker.Item label="Unknown" value="unknown" />
            <Picker.Item label="Male" value="male" />
            <Picker.Item label="Female" value="female" />
          </Picker> */}
        </View>
      </View>

      <View style={styles.fieldGroup}>
        <Text style={styles.label}>Status</Text>
        <View style={styles.pickerContainer}>
          {/* <Picker
            selectedValue={formData.status || 'healthy'}
            onValueChange={(value) => updateField('status', value)}
            style={styles.picker}
          >
            <Picker.Item label="Healthy" value="healthy" />
            <Picker.Item label="Sick" value="sick" />
            <Picker.Item label="Injured" value="injured" />
            <Picker.Item label="Pregnant" value="pregnant" />
          </Picker> */}
        </View>
      </View>

      <View style={styles.fieldGroup}>
        <Text style={styles.label}>Purpose</Text>
        <View style={styles.pickerContainer}>
          {/* <Picker
            selectedValue={formData.purpose || 'meat'}
            onValueChange={(value) => updateField('purpose', value)}
            style={styles.picker}
          >
            <Picker.Item label="Meat" value="meat" />
            <Picker.Item label="Dairy" value="dairy" />
            <Picker.Item label="Breeding" value="breeding" />
            <Picker.Item label="Work" value="work" />
          </Picker> */}
        </View>
      </View>

      <View style={styles.fieldGroup}>
        <Text style={styles.label}>Name</Text>
        <TextInput
          style={styles.input}
          value={formData.name || ''}
          onChangeText={(value) => updateField('name', value)}
          placeholder="Animal name (optional)"
        />
      </View>

      <View style={styles.row}>
        <View style={[styles.fieldGroup, { flex: 1, marginRight: 8 }]}>
          <Text style={styles.label}>Weight</Text>
          <TextInput
            style={styles.input}
            value={formData.weight?.toString() || ''}
            onChangeText={(value) => updateField('weight', parseFloat(value) || 0)}
            placeholder="0"
            keyboardType="numeric"
          />
        </View>
        <View style={[styles.fieldGroup, { flex: 1, marginLeft: 8 }]}>
          <Text style={styles.label}>Unit</Text>
          <View style={styles.pickerContainer}>
            {/* <Picker
              selectedValue={formData.weightUnit || 'kg'}
              onValueChange={(value) => updateField('weightUnit', value)}
              style={styles.picker}
            >
              <Picker.Item label="kg" value="kg" />
              <Picker.Item label="lbs" value="lbs" />
            </Picker> */}
          </View>
        </View>
      </View>

      <View style={styles.fieldGroup}>
        <Text style={styles.label}>Date of Birth</Text>
        <TouchableOpacity
          style={styles.dateButton}
          onPress={() => setShowDatePicker('dateOfBirth')}
        >
          <Text style={styles.dateButtonText}>
            {formData.dateOfBirth 
              ? new Date(formData.dateOfBirth).toLocaleDateString()
              : 'Select date'
            }
          </Text>
          <Ionicons name="calendar" size={20} color={colors.primary} />
        </TouchableOpacity>
      </View>

      <View style={styles.fieldGroup}>
        <Text style={styles.label}>Identification Number</Text>
        <TextInput
          style={styles.input}
          value={formData.identificationNumber || ''}
          onChangeText={(value) => updateField('identificationNumber', value)}
          placeholder="Tag number or ID"
        />
      </View>

      <View style={styles.fieldGroup}>
        <Text style={styles.label}>Notes</Text>
        <TextInput
          style={[styles.input, styles.textArea]}
          value={formData.notes || ''}
          onChangeText={(value) => updateField('notes', value)}
          placeholder="Additional notes..."
          multiline
          numberOfLines={3}
        />
      </View>
    </>
  );

  const renderPlantForm = () => (
    <>
      <Text style={styles.sectionTitle}>Plant Details</Text>
      
      <View style={styles.fieldGroup}>
        <Text style={styles.label}>Name *</Text>
        <TextInput
          style={styles.input}
          value={formData.name || ''}
          onChangeText={(value) => updateField('name', value)}
          placeholder="Plant name"
        />
      </View>

      <View style={styles.fieldGroup}>
        <Text style={styles.label}>Species *</Text>
        <TextInput
          style={styles.input}
          value={formData.species || ''}
          onChangeText={(value) => updateField('species', value)}
          placeholder="e.g., Tomato, Wheat, Corn"
        />
      </View>

      <View style={styles.fieldGroup}>
        <Text style={styles.label}>Variety</Text>
        <TextInput
          style={styles.input}
          value={formData.variety || ''}
          onChangeText={(value) => updateField('variety', value)}
          placeholder="e.g., Roma, Cherry"
        />
      </View>

      <View style={styles.fieldGroup}>
        <Text style={styles.label}>Planted Date *</Text>
        <TouchableOpacity
          style={styles.dateButton}
          onPress={() => setShowDatePicker('plantedDate')}
        >
          <Text style={styles.dateButtonText}>
            {formData.plantedDate 
              ? new Date(formData.plantedDate).toLocaleDateString()
              : 'Select date'
            }
          </Text>
          <Ionicons name="calendar" size={20} color={colors.primary} />
        </TouchableOpacity>
      </View>

      <View style={styles.fieldGroup}>
        <Text style={styles.label}>Status</Text>
        <View style={styles.pickerContainer}>
          {/* <Picker
            selectedValue={formData.status || 'growing'}
            onValueChange={(value) => updateField('status', value)}
            style={styles.picker}
          >
            <Picker.Item label="Seedling" value="seedling" />
            <Picker.Item label="Growing" value="growing" />
            <Picker.Item label="Flowering" value="flowering" />
            <Picker.Item label="Fruiting" value="fruiting" />
            <Picker.Item label="Harvested" value="harvested" />
          </Picker> */}
        </View>
      </View>

      <View style={styles.fieldGroup}>
        <Text style={styles.label}>Health</Text>
        <View style={styles.pickerContainer}>
          {/* <Picker
            selectedValue={formData.health || 'healthy'}
            onValueChange={(value) => updateField('health', value)}
            style={styles.picker}
          >
            <Picker.Item label="Healthy" value="healthy" />
            <Picker.Item label="Stressed" value="stressed" />
            <Picker.Item label="Diseased" value="diseased" />
            <Picker.Item label="Pest Damage" value="pest_damage" />
          </Picker> */}
        </View>
      </View>

      <View style={styles.fieldGroup}>
        <Text style={styles.label}>Expected Harvest Date</Text>
        <TouchableOpacity
          style={styles.dateButton}
          onPress={() => setShowDatePicker('expectedHarvestDate')}
        >
          <Text style={styles.dateButtonText}>
            {formData.expectedHarvestDate 
              ? new Date(formData.expectedHarvestDate).toLocaleDateString()
              : 'Select date'
            }
          </Text>
          <Ionicons name="calendar" size={20} color={colors.primary} />
        </TouchableOpacity>
      </View>

      <View style={styles.fieldGroup}>
        <Text style={styles.label}>Notes</Text>
        <TextInput
          style={[styles.input, styles.textArea]}
          value={formData.notes || ''}
          onChangeText={(value) => updateField('notes', value)}
          placeholder="Additional notes..."
          multiline
          numberOfLines={3}
        />
      </View>
    </>
  );

  const renderEquipmentForm = () => (
    <>
      <Text style={styles.sectionTitle}>Equipment Details</Text>
      
      <View style={styles.fieldGroup}>
        <Text style={styles.label}>Name *</Text>
        <TextInput
          style={styles.input}
          value={formData.name || ''}
          onChangeText={(value) => updateField('name', value)}
          placeholder="Equipment name"
        />
      </View>

      <View style={styles.fieldGroup}>
        <Text style={styles.label}>Type *</Text>
        <View style={styles.pickerContainer}>
          <DropdownPicker
            label="Equipment Type"
            options={[
              { label: 'Tractor', value: 'tractor' },
              { label: 'Harvester', value: 'harvester' },
              { label: 'Plow', value: 'plow' },
              { label: 'Sprayer', value: 'sprayer' },
              { label: 'Irrigation', value: 'irrigation' },
              { label: 'Tool', value: 'tool' },
              { label: 'Other', value: 'other' },
            ]}
            onSelect={(value) => updateField('type', value)}
            selectedValue={formData.type || 'tractor'}
            isMultiple={false}
          />
        </View>
      </View>

      <View style={styles.fieldGroup}>
        <Text style={styles.label}>Manufacturer</Text>
        <TextInput
          style={styles.input}
          value={formData.manufacturer || ''}
          onChangeText={(value) => updateField('manufacturer', value)}
          placeholder="Brand name"
        />
      </View>

      <View style={styles.fieldGroup}>
        <Text style={styles.label}>Model</Text>
        <TextInput
          style={styles.input}
          value={formData.model || ''}
          onChangeText={(value) => updateField('model', value)}
          placeholder="Model name/number"
        />
      </View>

      <View style={styles.fieldGroup}>
        <Text style={styles.label}>Status</Text>
        <View style={styles.pickerContainer}>
          {/* <Picker
            selectedValue={formData.status || 'operational'}
            onValueChange={(value) => updateField('status', value)}
            style={styles.picker}
          >
            <Picker.Item label="Operational" value="operational" />
            <Picker.Item label="Maintenance" value="maintenance" />
            <Picker.Item label="Repair" value="repair" />
            <Picker.Item label="Retired" value="retired" />
          </Picker> */}
        </View>
      </View>

      <View style={styles.fieldGroup}>
        <Text style={styles.label}>Purchase Date</Text>
        <TouchableOpacity
          style={styles.dateButton}
          onPress={() => setShowDatePicker('purchaseDate')}
        >
          <Text style={styles.dateButtonText}>
            {formData.purchaseDate 
              ? new Date(formData.purchaseDate).toLocaleDateString()
              : 'Select date'
            }
          </Text>
          <Ionicons name="calendar" size={20} color={colors.primary} />
        </TouchableOpacity>
      </View>

      <View style={styles.fieldGroup}>
        <Text style={styles.label}>Purchase Price</Text>
        <TextInput
          style={styles.input}
          value={formData.purchasePrice?.toString() || ''}
          onChangeText={(value) => updateField('purchasePrice', parseFloat(value) || 0)}
          placeholder="0.00"
          keyboardType="numeric"
        />
      </View>

      <View style={styles.fieldGroup}>
        <Text style={styles.label}>Notes</Text>
        <TextInput
          style={[styles.input, styles.textArea]}
          value={formData.notes || ''}
          onChangeText={(value) => updateField('notes', value)}
          placeholder="Additional notes..."
          multiline
          numberOfLines={3}
        />
      </View>
    </>
  );

  const renderFormByEntity = () => {
    switch (action.entity) {
      case 'animal':
        return renderAnimalForm();
      case 'plant':
        return renderPlantForm();
      case 'equipment':
        return renderEquipmentForm();
      // Add garden and field forms similarly
      default:
        return <Text>Form not implemented for {action.entity}</Text>;
    }
  };

  const validateForm = () => {
    const requiredFields = {
      animal: ['species'],
      plant: ['name', 'species', 'plantedDate'],
      equipment: ['name', 'type'],
      garden: ['name', 'type'],
      field: ['name', 'type'],
    };

    const required = requiredFields[action.entity] || [];
    const missing = required.filter(field => !formData[field]);

    if (missing.length > 0) {
      Alert.alert('Missing Required Fields', `Please fill in: ${missing.join(', ')}`);
      return false;
    }
    return true;
  };

  const handleSave = () => {
    if (validateForm()) {
      onSave(formData);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>{action.title}</Text>
        <Text style={styles.subtitle}>{action.description}</Text>
        <Text style={styles.confidence}>
          Confidence: {Math.round(action.confidence * 100)}%
        </Text>
      </View>

      <ScrollView style={styles.form}>
        {renderFormByEntity()}
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
          <Text style={styles.cancelButtonText}>Cancel</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
          <Text style={styles.saveButtonText}>Save</Text>
        </TouchableOpacity>
      </View>

      {showDatePicker && (
        <DateTimePicker
          value={formData[showDatePicker] ? new Date(formData[showDatePicker]) : new Date()}
          mode="date"
          display="default"
          onChange={(event, selectedDate) => {
            setShowDatePicker(null);
            if (selectedDate) {
              updateField(showDatePicker, selectedDate.toISOString());
            }
          }}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.gray[800],
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 8,
  },
  confidence: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '600',
  },
  form: {
    flex: 1,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.gray[800],
    marginBottom: 16,
  },
  fieldGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: colors.white,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    backgroundColor: colors.white,
  },
  picker: {
    height: 50,
  },
  dateButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    padding: 12,
    backgroundColor: colors.white,
  },
  dateButtonText: {
    fontSize: 16,
    color: colors.gray[800],
  },
  row: {
    flexDirection: 'row',
  },
  footer: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  cancelButton: {
    flex: 1,
    padding: 16,
    borderRadius: 8,
    backgroundColor: colors.gray[100],
    marginRight: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    color: colors.gray[800],
    fontWeight: '600',
  },
  saveButton: {
    flex: 1,
    padding: 16,
    borderRadius: 8,
    backgroundColor: colors.primary,
    marginLeft: 8,
    alignItems: 'center',
  },
  saveButtonText: {
    fontSize: 16,
    color: colors.white,
    fontWeight: '600',
  },
});