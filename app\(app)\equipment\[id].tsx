import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  SafeAreaView,
  Modal,
  FlatList,
} from 'react-native';
import { Stack, useLocalSearchParams, router } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import Button from '@/components/Button';
import HistoryTimeline from '@/components/HistoryTimeline';
import {
  ArrowLeft,
  Calendar,
  Clock,
  Edit,
  Trash2,
  Wrench,
  DollarSign,
  MapPin,
  Package,
  Factory,
  Gauge,
  AlertCircle,
  CheckCircle,
  AlertTriangle,
  XCircle,
  CheckSquare,
  Square,
  Camera,
  Image as ImageIcon,
  X,
  Archive,
} from 'lucide-react-native';
import ImagePicker from '@/components/ImagePicker';
import { uploadImageAsync } from '@/utils/firebase-storage';
import { analyzeEquipmentCondition } from '@/utils/openai-vision';
import InactiveStatusModal from '@/components/InactiveStatusModal';
import Toast from 'react-native-toast-message';
import { useTranslation } from '@/i18n/useTranslation';
import { useAuthStore } from '@/store/auth-store';
import EquipmentChecklistCard from '@/components/EquipmentChecklistCard';

export default function EquipmentDetailScreen() {
  const { id } = useLocalSearchParams();
  const { user } = useAuthStore()
  const { inventoryEquipment, fetchEquipmentFromInventory, saveEquipmentChecklist, getEquipmentChecklists,
    currentFarm, deleteInventoryItem, updateInventoryItem, loading } = useFarmStore();
  const [equipmentData, setEquipmentData] = useState<any>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [activeTab, setActiveTab] = useState('details');
  const [showImageModal, setShowImageModal] = useState(false);
  const [selectedImage, setSelectedImage] = useState('');
  const [newImageUri, setNewImageUri] = useState('');
  const [isAddingImage, setIsAddingImage] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [conditionAnalysis, setConditionAnalysis] = useState<any>(null);
  const [showInactiveModal, setShowInactiveModal] = useState(false);
  const { t, isRTL } = useTranslation();

  // Inventory status functions
  const getInventoryStatusColor = () => {
    if (!equipmentData) return colors.gray[500];

    if (equipmentData.quantity <= (equipmentData.minQuantity || 0)) {
      return colors.danger;
    }
    if (equipmentData.expiryDate && new Date(equipmentData.expiryDate) < new Date()) {
      return colors.warning;
    }
    return colors.success;
  };

  const getInventoryStatusIcon = () => {
    if (!equipmentData) return null;

    if (equipmentData.quantity <= (equipmentData.minQuantity || 0)) {
      return <AlertTriangle size={16} color={colors.danger} />;
    }
    if (equipmentData.expiryDate && new Date(equipmentData.expiryDate) < new Date()) {
      return <Clock size={16} color={colors.warning} />;
    }
    return <CheckCircle size={16} color={colors.success} />;
  };

  const getInventoryStatusText = () => {
    if (!equipmentData) return '';

    if (equipmentData.quantity <= (equipmentData.minQuantity || 0)) {
      return t('equipment.status.lowStock', 'Low Stock');
    }
    if (equipmentData.expiryDate && new Date(equipmentData.expiryDate) < new Date()) {
      return t('equipment.status.expired', 'Expired');
    }
    return t('equipment.status.available', 'Available');
  };
  // Checklist items
  const [checklist, setChecklist] = useState([
    { id: '1', title: t('checkOil'), value: 'checkOil', completed: false },
    { id: '2', title: t('inspectHydraulic'), value: 'inspectHydraulic', completed: false },
    { id: '3', title: t('checkTire'), value: 'checkTire', completed: false },
    { id: '4', title: t('inspectBelts'), value: 'inspectBelts', completed: false },
    { id: '5', title: t('checkBattery'), value: 'checkBattery', completed: false },
    { id: '6', title: t('inspectAirFilter'), value: 'inspectAirFilter', completed: false },
    { id: '7', title: t('checkLights'), value: 'checkLights', completed: false }
  ]);
  const [checklistItems, setChecklistItems] = useState([])
  const callCheckListData = async () => {
    const eqiuplemntlist = await getEquipmentChecklists(currentFarm.id, id as string)
    // console.log({ eqiuplemntlist })
    setChecklistItems(eqiuplemntlist)
  }
  useEffect(() => {
    if (id && inventoryEquipment) {
      const data = inventoryEquipment.find(eq => eq.id === id);
      callCheckListData()
      setEquipmentData(data);
    }
  }, [id, inventoryEquipment]);

  const handleDelete = () => {
    Alert.alert(
      'Delete Equipment',
      'Are you sure you want to delete this equipment? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              setIsDeleting(true);
              await deleteInventoryItem(id as string);
              Toast.show({
                type: 'success',
                text1: 'Success',
                text2: 'Equipment deleted successfully',
              });
              router.back();
            } catch (error) {
              console.error('Error deleting equipment:', error);
              Toast.show({
                type: 'error',
                text1: 'Error',
                text2: 'Failed to delete equipment',
              });
            } finally {
              setIsDeleting(false);
            }
          }
        }
      ]
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'operational':
        return colors.success;
      case 'maintenance':
        return colors.warning;
      case 'repair':
        return colors.danger;
      case 'retired':
        return colors.gray[500];
      default:
        return colors.gray[500];
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'operational':
        return <CheckCircle size={20} color={colors.success} />;
      case 'maintenance':
        return <AlertTriangle size={20} color={colors.warning} />;
      case 'repair':
        return <AlertCircle size={20} color={colors.danger} />;
      case 'retired':
        return <XCircle size={20} color={colors.gray[500]} />;
      default:
        return <AlertCircle size={20} color={colors.gray[500]} />;
    }
  };

  const toggleChecklistItem = (id: string) => {
    setChecklist(checklist.map(item =>
      item.id === id ? { ...item, completed: !item.completed } : item
    ));
  };
  const completedCount = checklist.filter(item => item.completed).length;
  const totalCount = checklist.length;
  const progress = totalCount > 0 ? (completedCount / totalCount) * 100 : 0;
  const handleAddImage = async () => {
    if (!newImageUri) {
      Alert.alert('Error', 'Please select an image first');
      return;
    }

    try {
      setIsAddingImage(true);

      // Upload the new image
      const imageUrl = await uploadImageAsync(newImageUri, 'equipment');

      // Add the new photo to the equipment's photos array
      const updatedPhotos = [
        ...(equipmentData.photos || []),
        {
          url: imageUrl,
          timestamp: new Date(),
          takenBy: ''
        }
      ];

      // Update the equipment with the new photos array
      const updatedEquipment = {
        ...equipmentData,
        photos: updatedPhotos,
        updatedAt: new Date()
      };

      await updateEquipment(equipmentData.id, updatedEquipment);
      setEquipmentData(updatedEquipment);
      setNewImageUri('');
      setIsAddingImage(false);
      Alert.alert('Success', 'Image added successfully');
    } catch (error) {
      console.error('Error adding image:', error);
      Alert.alert('Error', 'Failed to add image');
      setIsAddingImage(false);
    }
  };

  const handleAnalyzeCondition = async () => {
    if (!equipmentData.image && (!equipmentData.photos || equipmentData.photos.length === 0)) {
      Alert.alert('Error', 'No image available for analysis');
      return;
    }

    try {
      setIsAnalyzing(true);

      // Use the main image or the first photo
      const imageToAnalyze = equipmentData.image || equipmentData.photos[0].url;

      // Call the OpenAI Vision API
      const analysis = await analyzeEquipmentCondition(imageToAnalyze);

      if (analysis) {
        setConditionAnalysis(analysis);
        setActiveTab('condition');
      } else {
        Alert.alert('Error', 'Failed to analyze equipment condition');
      }
    } catch (error) {
      console.error('Error analyzing equipment condition:', error);
      Alert.alert('Error', 'Failed to analyze equipment condition');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleMarkInactive = async (data: {
    reason: string;
    notes?: string;
    image?: string;
  }) => {
    try {
      await markEquipmentInactive(id as string, data);
      Toast.show({
        type: 'success',
        text1: 'Success',
        text2: 'Equipment marked as inactive',
      });
    } catch (error) {
      console.error('Error marking equipment as inactive:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to mark equipment as inactive',
      });
    }
  };

  const renderDetailsTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.infoSection}>
        <Text style={[styles.sectionTitle, isRTL && { textAlign: 'right' }]}>
          {t('entity.equipment.title', 'Equipment Details')}
        </Text>

        {/* Category */}
        <View style={[styles.infoRow, isRTL && { flexDirection: 'row-reverse' }]}>
          <Package size={20} color={colors.gray[600]} />
          <Text style={[styles.infoLabel, isRTL && { textAlign: 'right', marginRight: 8 }]}>
            {t('equipment.category', 'Category')}:
          </Text>
          <Text style={[styles.infoValue, isRTL && { textAlign: 'right', marginRight: 8 }]}>
            {equipmentData.category}
          </Text>
        </View>

        {/* Quantity */}
        <View style={[styles.infoRow, isRTL && { flexDirection: 'row-reverse' }]}>
          <Package size={20} color={colors.gray[600]} />
          <Text style={[styles.infoLabel, isRTL && { textAlign: 'right', marginRight: 8 }]}>
            {t('equipment.quantity', 'Quantity')}:
          </Text>
          <Text style={[styles.infoValue, isRTL && { textAlign: 'right', marginRight: 8 }]}>
            {equipmentData.quantity} {equipmentData.unit}
          </Text>
        </View>

        {/* Price */}
        {equipmentData?.price && (
          <View style={[styles.infoRow, isRTL && { flexDirection: 'row-reverse' }]}>
            <DollarSign size={20} color={colors.gray[600]} />
            <Text style={[styles.infoLabel, isRTL && { textAlign: 'right', marginRight: 8 }]}>
              {t('equipment.price', 'Price')}:
            </Text>
            <Text style={[styles.infoValue, isRTL && { textAlign: 'right', marginRight: 8 }]}>
              ${equipmentData.price}
            </Text>
          </View>
        )}

        {/* Supplier */}
        {equipmentData?.supplier && (
          <View style={[styles.infoRow, isRTL && { flexDirection: 'row-reverse' }]}>
            <Factory size={20} color={colors.gray[600]} />
            <Text style={[styles.infoLabel, isRTL && { textAlign: 'right', marginRight: 8 }]}>
              {t('equipment.supplier', 'Supplier')}:
            </Text>
            <Text style={[styles.infoValue, isRTL && { textAlign: 'right', marginRight: 8 }]}>
              {equipmentData.supplier}
            </Text>
          </View>
        )}

        {/* Purchase Date */}
        {equipmentData?.purchaseDate && (
          <View style={[styles.infoRow, isRTL && { flexDirection: 'row-reverse' }]}>
            <Calendar size={20} color={colors.gray[600]} />
            <Text style={[styles.infoLabel, isRTL && { textAlign: 'right', marginRight: 8 }]}>
              {t('equipment.purchaseDate', 'Purchase Date')}:
            </Text>
            <Text style={[styles.infoValue, isRTL && { textAlign: 'right', marginRight: 8 }]}>
              {new Date(equipmentData.purchaseDate).toLocaleDateString()}
            </Text>
          </View>
        )}

        {/* Expiry Date */}
        {equipmentData?.expiryDate && (
          <View style={[styles.infoRow, isRTL && { flexDirection: 'row-reverse' }]}>
            <Clock size={20} color={colors.gray[600]} />
            <Text style={[styles.infoLabel, isRTL && { textAlign: 'right', marginRight: 8 }]}>
              {t('equipment.expiryDate', 'Expiry Date')}:
            </Text>
            <Text style={[styles.infoValue, isRTL && { textAlign: 'right', marginRight: 8 }]}>
              {new Date(equipmentData.expiryDate).toLocaleDateString()}
            </Text>
          </View>
        )}

        {/* Minimum Quantity */}
        {equipmentData?.minQuantity && (
          <View style={[styles.infoRow, isRTL && { flexDirection: 'row-reverse' }]}>
            <AlertTriangle size={20} color={colors.gray[600]} />
            <Text style={[styles.infoLabel, isRTL && { textAlign: 'right', marginRight: 8 }]}>
              {t('equipment.minQuantity', 'Min Quantity')}:
            </Text>
            <Text style={[styles.infoValue, isRTL && { textAlign: 'right', marginRight: 8 }]}>
              {equipmentData.minQuantity} {equipmentData.unit}
            </Text>
          </View>
        )}

        {/* Consumable Badge */}
        {equipmentData?.isConsumable && (
          <View style={[styles.infoRow, isRTL && { flexDirection: 'row-reverse' }]}>
            <CheckCircle size={20} color={colors.info} />
            <Text style={[styles.infoLabel, isRTL && { textAlign: 'right', marginRight: 8 }]}>
              {t('equipment.consumable', 'Consumable')}:
            </Text>
            <Text style={[styles.infoValue, { color: colors.info }, isRTL && { textAlign: 'right', marginRight: 8 }]}>
              {t('common.yes', 'Yes')}
            </Text>
          </View>
        )}

        {/* Location */}
        {equipmentData?.location && (
          <View style={[styles.infoRow, isRTL && { flexDirection: 'row-reverse' }]}>
            <MapPin size={20} color={colors.gray[600]} />
            <Text style={[styles.infoLabel, isRTL && { textAlign: 'right', marginRight: 8 }]}>
              {t('equipment.location', 'Location')}:
            </Text>
            <Text style={[styles.infoValue, isRTL && { textAlign: 'right', marginRight: 8 }]}>
              {equipmentData.location}
            </Text>
          </View>
        )}
      </View>

      {/* Description */}
      {equipmentData.description && equipmentData.description !== 'No description' && (
        <View style={[styles.notesSection]}>
          <Text style={[styles.sectionTitle, isRTL && { textAlign: 'right', marginRight: 8 }]}>
            {t('common.description', 'Description')}
          </Text>
          <Text style={[styles.notesText, isRTL && { textAlign: 'right', marginRight: 8 }]}>
            {equipmentData.description}
          </Text>
        </View>
      )}


      <View style={styles.quickActionsContainer}>
        <Text style={[styles.sectionTitle, isRTL && { textAlign: 'right' }]}>
          {t('entity.equipment.quickActions.title')}
        </Text>

        <View style={styles.quickActions}>
          <TouchableOpacity
            onPress={() =>
              router.push({
                pathname: '/equipment/create',
                params: { editMode: true, id: id }
              })
            }
            style={[styles.quickActionButton, isRTL && { flexDirection: 'row-reverse' }]}
          >
            <View style={[styles.quickActionIcon, { backgroundColor: colors.success }]}>
              <Edit size={20} color={colors.white} />
            </View>
            <Text style={[styles.quickActionText, isRTL && { textAlign: 'right', marginRight: 8 }]}>{t('entity.equipment.quickActions.edit')}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => setShowInactiveModal(true)}
            // style={styles.quickActionButton}
            style={[styles.quickActionButton, isRTL && { flexDirection: 'row-reverse' }]}
          >
            <View style={[styles.quickActionIcon, { backgroundColor: colors.gray[700] }]}>
              <Archive size={20} color={colors.white} />
            </View>
            <Text style={[styles.quickActionText, isRTL && { textAlign: 'right', marginRight: 8 }]}>{t('entity.equipment.quickActions.inactive')}</Text>
          </TouchableOpacity>


          <TouchableOpacity
            onPress={() => router.push(`/finance/create?id=${id}&entity=equipment`)}
            // style={styles.quickActionButton}
            style={[styles.quickActionButton, isRTL && { flexDirection: 'row-reverse' }]}
          >
            <View style={[styles.quickActionIcon, { backgroundColor: colors.gray[700] }]}>
              <Archive size={20} color={colors.white} />
            </View>
            <Text style={[styles.quickActionText, isRTL && { textAlign: 'right', marginRight: 8 }]}>{t('entity.equipment.quickActions.addExpense')}</Text>
          </TouchableOpacity>


        </View>
      </View>
      <View style={styles.photosSection}>
        <Text style={[styles.sectionTitle, isRTL && { textAlign: 'right' }]}>
          {t('entity.equipment.photos.title')}
        </Text>

        <View style={styles.addPhotoContainer}>
          <ImagePicker
            image={newImageUri}
            onImageSelected={setNewImageUri}
            placeholder={t('entity.equipment.photos.addPlaceholder')}
            size={120}
          />

          <Button
            title={isAddingImage ? t('entity.equipment.photos.adding') : t('entity.equipment.photos.add')}
            onPress={handleAddImage}
            disabled={!newImageUri || isAddingImage}
            style={styles.addPhotoButton}
            leftIcon={
              isAddingImage ? <ActivityIndicator size="small" color={colors.white} /> : undefined
            }
          />
        </View>

        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={[
            styles.photosContainer,
            isRTL && { flexDirection: 'row-reverse' }
          ]}
        >
          {equipmentData.photos && equipmentData.photos.length > 0 ? (
            equipmentData.photos.map((photo: any, index: number) => (
              <TouchableOpacity
                key={index}
                style={styles.photoItem}
                onPress={() => {
                  setSelectedImage(photo.url);
                  setShowImageModal(true);
                }}
              >
                <Image
                  source={{ uri: photo.url }}
                  style={styles.photoImage}
                  resizeMode="cover"
                />
                {photo.timestamp && (
                  <Text style={styles.photoDate}>
                    {new Date(photo.timestamp).toLocaleDateString()}
                  </Text>
                )}
              </TouchableOpacity>
            ))
          ) : (
            <View style={styles.noPhotosContainer}>
              <Camera size={24} color={colors.gray[400]} />
              <Text style={[styles.noPhotosText, isRTL && { textAlign: 'right' }]}>
                {t('entity.equipment.photos.noPhotos')}
              </Text>
            </View>
          )}
        </ScrollView>
      </View>
      <View style={styles.actionsContainer}>
        <Button
          title={t('entity.equipment.actions.scheduleMaintenance')}
          leftIcon={<Wrench size={20} color={colors.white} />}
          onPress={() => {
            router.push({
              pathname: '/task/create',
              params: {
                entityType: 'equipment',
                entityId: id,
                category: 'maintenance'
              }
            });
          }}
          style={styles.actionButton}
        />
      </View>
    </View>
  );

  const renderChecklistTab = () => (
    // <View style={styles.tabContent}>
    //   <View style={styles.checklistContainer}>
    //     {checklist.map((item) => (
    //       <TouchableOpacity
    //         key={item.id}
    //         style={styles.checklistItem}
    //         onPress={() => toggleChecklistItem(item.id)}
    //       >
    //         <View style={styles.checkbox}>
    //           {item.completed ? (
    //             <CheckSquare size={20} color={colors.primary} />
    //           ) : (
    //             <Square size={20} color={colors.gray[400]} />
    //           )}
    //         </View>
    //         <View style={styles.checklistItemContent}>
    //           <Text style={[
    //             styles.checklistItemTitle,
    //             item.completed && styles.checklistItemCompleted
    //           ]}>
    //             {item.title}
    //           </Text>
    //         </View>
    //       </TouchableOpacity>
    //     ))}

    //     <View style={styles.checklistSummary}>
    //       <Text style={styles.checklistSummaryText}>
    //         {checklist.filter(item => item.completed).length} of {checklist.length} tasks completed
    //       </Text>
    //       <View style={styles.progressBarContainer}>
    //         <View
    //           style={[
    //             styles.progressBar,
    //             {
    //               width: `${(checklist.filter(item => item.completed).length / checklist.length) * 100}%`,
    //               backgroundColor: colors.primary
    //             }
    //           ]}
    //         />
    //       </View>
    //     </View>
    //   </View>

    //   <View style={styles.checklistActions}>
    //     <Button
    //       title="Save Checklist"
    //       onPress={() => {
    //         Alert.alert('Success', 'Checklist saved successfully');
    //       }}
    //       style={styles.saveChecklistButton}
    //     />
    //   </View>
    // </View>

    <View style={styles.tabContent}>
      {
        user.role === "caretaker" ?
          <>
            <View style={styles.checklistContainer}>
              {checklist.map((item) => (
                <TouchableOpacity
                  key={item.id}
                  style={[styles.checklistItem, isRTL && { flexDirection: 'row-reverse' }]}
                  onPress={() => toggleChecklistItem(item.id)}
                >
                  <View style={styles.checkbox}>
                    {item.completed ? (
                      <CheckSquare size={20} color={colors.primary} />
                    ) : (
                      <Square size={20} color={colors.gray[400]} />
                    )}
                  </View>

                  <View style={[styles.checklistItemContent, isRTL && { marginRight: 10 }]}>
                    <Text
                      style={[
                        styles.checklistItemTitle,
                        item.completed && styles.checklistItemCompleted,
                        isRTL && { textAlign: 'right' }
                      ]}
                    >

                      {t(`entity.equipment.checklistItems.${item.title}`, item.title)}
                    </Text>
                  </View>
                </TouchableOpacity>
              ))}

              <View style={styles.checklistSummary}>
                <Text style={[styles.checklistSummaryText, isRTL && { textAlign: 'right' }]}>
                  {t('entity.equipment.summary', {
                    completed: completedCount,
                    total: totalCount
                  })}
                </Text>

                <View style={styles.progressBarContainer}>
                  <View
                    style={[
                      styles.progressBar,
                      {
                        width: `${progress}%`,
                        backgroundColor: colors.primary
                      }
                    ]}
                  />
                </View>
              </View>
            </View>

            <View style={styles.checklistActions}>
              <Button
                title={t('entity.equipment.saveButton', 'Save Checklist')}
                onPress={async () => {

                  const res = await saveEquipmentChecklist(currentFarm.id, id, user.id, checklist)
                  if (res == -1) {
                    Alert.alert('error', res.message)
                  } else {
                    Alert.alert(t('entity.equipment.successTitle', 'Success'), t('entity.equipment..successMessage', 'Checklist saved successfully'));
                  }
                  // 
                }}
                style={styles.saveChecklistButton}
              />
            </View>
          </>
          :
          <>

            <FlatList
              data={checklistItems}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => <EquipmentChecklistCard checklist={item} />}
            />
          </>
      }

    </View>
  );

  const renderConditionTab = () => (
    <View style={styles.tabContent}>
      {!conditionAnalysis ? (
        <View style={styles.conditionAnalysisContainer}>
          <View style={styles.noAnalysisContainer}>
            <AlertCircle size={40} color={colors.gray[400]} />

            <Text style={styles.noAnalysisText}>
              {t('entity.equipment.noAnalysisTitle')}
            </Text>

            <Text style={styles.noAnalysisSubtext}>
              {t('entity.equipment.noAnalysisDescription')}
            </Text>

            <Button
              title={isAnalyzing ? t('entity.equipment.analyzing') : t('entity.equipment.analyzeButton')}
              onPress={handleAnalyzeCondition}
              disabled={isAnalyzing}
              style={styles.analyzeButton}
              leftIcon={
                isAnalyzing ? <ActivityIndicator size="small" color={colors.white} /> : undefined
              }
            />
          </View>
        </View>
      ) : (
        <View style={styles.conditionAnalysisContainer}>
          <View style={styles.conditionStatusContainer}>
            <View style={[
              styles.conditionStatusBadge,
              {
                backgroundColor:
                  conditionAnalysis.condition === 'excellent' ? colors.success + '20' :
                    conditionAnalysis.condition === 'good' ? colors.primary + '20' :
                      conditionAnalysis.condition === 'fair' ? colors.warning + '20' :
                        colors.danger + '20'
              }
            ]}>
              <CheckCircle size={20} color={
                conditionAnalysis.condition === 'excellent' ? colors.success :
                  conditionAnalysis.condition === 'good' ? colors.primary :
                    conditionAnalysis.condition === 'fair' ? colors.warning :
                      colors.danger
              } />
              <Text style={[
                styles.conditionStatusText,
                {
                  color:
                    conditionAnalysis.condition === 'excellent' ? colors.success :
                      conditionAnalysis.condition === 'good' ? colors.primary :
                        conditionAnalysis.condition === 'fair' ? colors.warning :
                          colors.danger
                }
              ]}>
                {t(`entity.equipment.conditions.${conditionAnalysis.condition}`)}
              </Text>
            </View>
          </View>

          {conditionAnalysis.maintenanceNeeds?.length > 0 && (
            <View style={styles.conditionSection}>
              <Text style={styles.conditionSectionTitle}>{t('entity.equipment.maintenanceNeeds')}</Text>
              {conditionAnalysis.maintenanceNeeds.map((need, index) => (
                <View key={index} style={styles.maintenanceItem}>
                  <View style={styles.maintenanceHeader}>
                    <Text style={styles.maintenanceComponent}>{need.component}</Text>
                    <View style={[
                      styles.urgencyBadge,
                      {
                        backgroundColor:
                          need.urgency === 'High' ? colors.danger + '20' :
                            need.urgency === 'Medium' ? colors.warning + '20' :
                              colors.success + '20'
                      }
                    ]}>
                      <Text style={[
                        styles.urgencyText,
                        {
                          color:
                            need.urgency === 'High' ? colors.danger :
                              need.urgency === 'Medium' ? colors.warning :
                                colors.success
                        }
                      ]}>
                        {t(`entity.equipment.urgency.${need.urgency.toLowerCase()}`)}
                      </Text>
                    </View>
                  </View>
                  <Text style={styles.maintenanceIssue}>{need.issue}</Text>
                  <Text style={styles.maintenanceRecommendation}>{need.recommendation}</Text>
                </View>
              ))}
            </View>
          )}

          <View style={styles.conditionSection}>
            <Text style={styles.conditionSectionTitle}>{t('entity.equipment.estimatedLife')}</Text>
            <Text style={styles.conditionDetailsText}>{conditionAnalysis.estimatedLifeRemaining}</Text>
          </View>

          <View style={styles.conditionSection}>
            <Text style={styles.conditionSectionTitle}>{t('entity.equipment.details')}</Text>
            <Text style={styles.conditionDetailsText}>{conditionAnalysis.details}</Text>
          </View>

          <View style={styles.conditionActions}>
            <Button
              title={t('entity.equipment.scheduleMaintenance')}
              onPress={() => {
                router.push({
                  pathname: '/task/create',
                  params: {
                    title: `${t('entity.equipment.maintenanceTitlePrefix')} ${equipmentData.name}`,
                    category: 'maintenance',
                    priority: 'medium'
                  }
                });
              }}
              style={styles.conditionActionButton}
              leftIcon={<Wrench size={20} color={colors.white} />}
            />
          </View>
        </View>
      )}
    </View>
  );

  const renderHistoryTab = () => (
    <View style={styles.tabContent}>
      <View style={styles.historySection}>
        {/* <Text style={styles.sectionTitle}>Maintenance History</Text> */}
        <Text style={[styles.sectionTitle, isRTL && { textAlign: 'right' }]}>
          {t('entity.equipment.title')}
        </Text>
        <HistoryTimeline
          items={[
            {
              id: '1',
              title: 'Regular Maintenance',
              description: 'Oil change and filter replacement',
              date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
              type: 'maintenance'
            },
            {
              id: '2',
              title: 'Repair',
              description: 'Fixed hydraulic system leak',
              date: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
              type: 'repair'
            }
          ]}
        />
      </View>
    </View>
  );

  if (loading || !equipmentData) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Loading equipment details...</Text>
      </SafeAreaView>
    );
  }

  return (
    <>
      <Stack.Screen
        options={{
          title: equipmentData.name,
          headerShown: true,
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
              <ArrowLeft size={24} color={colors.gray[800]} />
            </TouchableOpacity>
          ),
          headerRight: () => (
            <View style={styles.headerButtons}>
              <TouchableOpacity
                onPress={() => router.push({
                  pathname: '/equipment/create',
                  params: { editMode: true, equipmentId: id }
                })}
                style={styles.headerButton}
              >
                <Edit size={20} color={colors.primary} />
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => setShowInactiveModal(true)}
                style={styles.headerButton}
              >
                <Archive size={20} color={colors.gray[700]} />
              </TouchableOpacity>
              <TouchableOpacity
                onPress={handleDelete}
                style={styles.headerButton}
                disabled={isDeleting}
              >
                {isDeleting ? (
                  <ActivityIndicator size="small" color={colors.danger} />
                ) : (
                  <Trash2 size={20} color={colors.danger} />
                )}
              </TouchableOpacity>
            </View>
          ),
        }}
      />

      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: equipmentData.imageUrl || 'https://images.unsplash.com/photo-1605000797499-95a51c5269ae?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxzZWFyY2h8Mnx8dHJhY3RvcnxlbnwwfHwwfHw%3D&w=1000&q=80' }}
            style={styles.coverImage}
            resizeMode="cover"
          />

          <View style={styles.imageOverlay}>
            <TouchableOpacity
              style={styles.viewImagesButton}
              onPress={() => {
                if (equipmentData.imageUrl) {
                  setSelectedImage(equipmentData.imageUrl);
                  setShowImageModal(true);
                }
              }}
            >
              <ImageIcon size={20} color={colors.white} />
              <Text style={styles.viewImagesText}>
                {equipmentData.imageUrl ? 1 : 0} Photos
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.detailsContainer}>
          <View style={styles.header}>
            <Text style={styles.name}>{equipmentData.name}</Text>
            <View style={styles.typeContainer}>
              <Text style={styles.type}>
                {equipmentData.category}
              </Text>
            </View>
          </View>

          <View style={styles.statusContainer}>
            <View style={styles.statusBadge}>
              {getInventoryStatusIcon()}
              <Text style={[
                styles.statusText,
                { color: getInventoryStatusColor() }
              ]}>
                {getInventoryStatusText()}
              </Text>
            </View>
          </View>
          <View style={[styles.tabsContainer, isRTL && { flexDirection: 'row-reverse' }]}>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'details' && styles.activeTab]}
              onPress={() => setActiveTab('details')}
            >
              <Text style={[styles.tabText, activeTab === 'details' && styles.activeTabText]}>
                {t('entity.equipment.tabs.details')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.tab, activeTab === 'checklist' && styles.activeTab]}
              onPress={() => setActiveTab('checklist')}
            >
              <Text style={[styles.tabText, activeTab === 'checklist' && styles.activeTabText]}>
                {t('entity.equipment.tabs.checklist')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.tab, activeTab === 'condition' && styles.activeTab]}
              onPress={() => setActiveTab('condition')}
            >
              <Text style={[styles.tabText, activeTab === 'condition' && styles.activeTabText]}>
                {t('entity.equipment.tabs.condition')}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.tab, activeTab === 'history' && styles.activeTab]}
              onPress={() => setActiveTab('history')}
            >
              <Text style={[styles.tabText, activeTab === 'history' && styles.activeTabText]}>
                {t('entity.equipment.tabs.history')}
              </Text>
            </TouchableOpacity>
          </View>

          {activeTab === 'details' && renderDetailsTab()}
          {activeTab === 'checklist' && renderChecklistTab()}
          {activeTab === 'condition' && renderConditionTab()}
          {activeTab === 'history' && renderHistoryTab()}


        </View>
      </ScrollView >

      {/* Image Viewer Modal */}
      < Modal
        visible={showImageModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowImageModal(false)
        }
      >
        <View style={styles.imageModalOverlay}>
          <TouchableOpacity
            style={styles.closeImageButton}
            onPress={() => setShowImageModal(false)}
          >
            <X size={24} color={colors.white} />
          </TouchableOpacity>

          <Image
            source={{ uri: selectedImage }}
            style={styles.fullImage}
            resizeMode="contain"
          />

          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.imageThumbnails}
          >
            {equipmentData.photos && equipmentData.photos.map((photo: any, index: number) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.thumbnailContainer,
                  selectedImage === photo.url && styles.selectedThumbnail
                ]}
                onPress={() => setSelectedImage(photo.url)}
              >
                <Image
                  source={{ uri: photo.url }}
                  style={styles.thumbnail}
                  resizeMode="cover"
                />
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </Modal >

      {/* Inactive Status Modal */}
      < InactiveStatusModal
        visible={showInactiveModal}
        onClose={() => setShowInactiveModal(false)}
        onSubmit={handleMarkInactive}
        entityType="equipment"
        showCascadeOption={false}
      />
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  contentContainer: {
    paddingBottom: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: colors.gray[600],
  },
  backButton: {
    padding: 8,
  },
  headerButtons: {
    flexDirection: 'row',
  },
  headerButton: {
    padding: 8,
    marginLeft: 8,
  },
  imageContainer: {
    position: 'relative',
    height: 250,
  },
  coverImage: {
    width: '100%',
    height: '100%',
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    padding: 16,
  },
  viewImagesButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  viewImagesText: {
    color: colors.white,
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  detailsContainer: {
    padding: 16,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: -20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  name: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.gray[900],
    flex: 1,
  },
  typeContainer: {
    backgroundColor: colors.gray[100],
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
  },
  type: {
    fontSize: 14,
    color: colors.gray[700],
    fontWeight: '500',
  },
  statusContainer: {
    marginBottom: 16,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: colors.gray[100],
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  tabsContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
    marginBottom: 16,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
  },
  tabText: {
    fontSize: 14,
    color: colors.gray[600],
  },
  activeTabText: {
    color: colors.primary,
    fontWeight: '600',
  },
  tabContent: {
    marginBottom: 16,
  },
  infoSection: {
    backgroundColor: colors.gray[50],
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 12,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginLeft: 8,
    width: 120,
  },
  infoValue: {
    fontSize: 14,
    color: colors.gray[800],
    flex: 1,
  },
  notesSection: {
    backgroundColor: colors.gray[50],
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  notesText: {
    fontSize: 14,
    color: colors.gray[800],
    lineHeight: 20,
  },
  historySection: {
    backgroundColor: colors.gray[50],
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  actionsContainer: {
    marginTop: 8,
  },
  actionButton: {
    marginBottom: 12,
  },
  quickActionsContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  quickActionButton: {
    width: '48%',
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  quickActionIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  quickActionText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[800],
  },
  quickActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  photosSection: {
    marginBottom: 20,
  },
  photosContainer: {
    paddingBottom: 16,
  },
  photoItem: {
    marginRight: 12,
    width: 120,
    height: 120,
    borderRadius: 8,
    overflow: 'hidden',
  },
  photoImage: {
    width: '100%',
    height: '100%',
  },
  photoDate: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    color: colors.white,
    fontSize: 10,
    padding: 4,
    textAlign: 'center',
  },
  noPhotosContainer: {
    width: 120,
    height: 120,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
  },
  noPhotosText: {
    fontSize: 12,
    color: colors.gray[500],
    marginTop: 8,
    textAlign: 'center',
  },
  addPhotoContainer: {
    marginBottom: 16,
  },
  addPhotoButton: {
    marginTop: 12,
  },
  imageModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeImageButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    zIndex: 10,
  },
  fullImage: {
    width: '100%',
    height: '70%',
  },
  imageThumbnails: {
    position: 'absolute',
    bottom: 20,
    paddingHorizontal: 20,
  },
  thumbnailContainer: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginHorizontal: 8,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedThumbnail: {
    borderColor: colors.primary,
  },
  thumbnail: {
    width: '100%',
    height: '100%',
  },
  checklistContainer: {
    backgroundColor: colors.gray[50],
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  checklistItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  checkbox: {
    marginRight: 12,
  },
  checklistItemContent: {
    flex: 1,
  },
  checklistItemTitle: {
    fontSize: 14,
    color: colors.gray[800],
  },
  checklistItemCompleted: {
    textDecorationLine: 'line-through',
    color: colors.gray[500],
  },
  checklistSummary: {
    marginTop: 16,
  },
  checklistSummaryText: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 8,
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: colors.gray[200],
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 4,
  },
  checklistActions: {
    marginBottom: 20,
  },
  saveChecklistButton: {
    backgroundColor: colors.success,
  },
  conditionAnalysisContainer: {
    marginBottom: 20,
  },
  noAnalysisContainer: {
    backgroundColor: colors.gray[50],
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
  },
  noAnalysisText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginTop: 12,
    marginBottom: 8,
  },
  noAnalysisSubtext: {
    fontSize: 14,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: 20,
  },
  analyzeButton: {
    width: '100%',
    backgroundColor: colors.primary,
  },
  conditionStatusContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  conditionStatusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  conditionStatusText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  conditionSection: {
    backgroundColor: colors.gray[50],
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  conditionSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 12,
  },
  maintenanceItem: {
    marginBottom: 16,
    padding: 12,
    backgroundColor: colors.white,
    borderRadius: 8,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  maintenanceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  maintenanceComponent: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[800],
  },
  urgencyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  urgencyText: {
    fontSize: 12,
    fontWeight: '500',
  },
  maintenanceIssue: {
    fontSize: 14,
    color: colors.gray[700],
    marginBottom: 4,
  },
  maintenanceRecommendation: {
    fontSize: 14,
    color: colors.gray[600],
    fontStyle: 'italic',
  },
  conditionDetailsText: {
    fontSize: 14,
    lineHeight: 20,
    color: colors.gray[700],
  },
  conditionActions: {
    marginTop: 8,
  },
  conditionActionButton: {
    backgroundColor: colors.primary,
  },
});






