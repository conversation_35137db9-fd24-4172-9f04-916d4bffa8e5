import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '@/constants/colors';
import { useTranslation } from '@/i18n/useTranslation';

interface QuickCommand {
  id: string;
  title: string;
  icon: string;
  command: string;
  color: string;
}

interface AIQuickCommandsProps {
  onCommandPress: (command: string) => void;
  visible?: boolean;
}

export default function AIQuickCommands({ 
  onCommandPress, 
  visible = true 
}: AIQuickCommandsProps) {
  const { t, isRTL } = useTranslation();

  const quickCommands: QuickCommand[] = [
    {
      id: 'show-animals',
      title: t('ai.commands.showAnimals', 'Show my animals'),
      icon: 'paw',
      command: t('ai.commands.showAnimals', 'Show my animals'),
      color: colors.success,
    },
    {
      id: 'show-plants',
      title: t('ai.commands.showPlants', 'Show my plants'),
      icon: 'leaf',
      command: t('ai.commands.showPlants', 'Show my plants'),
      color: colors.primary,
    },
    {
      id: 'show-fields',
      title: t('ai.commands.showFields', 'Show my fields'),
      icon: 'grid',
      command: t('ai.commands.showFields', 'Show my fields'),
      color: colors.warning,
    },
    {
      id: 'show-gardens',
      title: t('ai.commands.showGardens', 'Show my gardens'),
      icon: 'flower',
      command: t('ai.commands.showGardens', 'Show my gardens'),
      color: colors.secondary,
    },
    {
      id: 'show-equipment',
      title: t('ai.commands.showEquipment', 'Show my equipment'),
      icon: 'construct',
      command: t('ai.commands.showEquipment', 'Show my equipment'),
      color: colors.gray[600],
    },
    {
      id: 'add-animal',
      title: t('ai.commands.addAnimal', 'Add new animal'),
      icon: 'add-circle',
      command: t('ai.commands.addAnimal', 'Add new animal'),
      color: colors.success,
    },
    {
      id: 'add-plant',
      title: t('ai.commands.addPlant', 'Add new plant'),
      icon: 'add-circle',
      command: t('ai.commands.addPlant', 'Add new plant'),
      color: colors.primary,
    },
    {
      id: 'add-field',
      title: t('ai.commands.addField', 'Add new field'),
      icon: 'add-circle',
      command: t('ai.commands.addField', 'Add new field'),
      color: colors.warning,
    },
  ];

  if (!visible) return null;

  const renderCommand = (command: QuickCommand) => (
    <TouchableOpacity
      key={command.id}
      style={[
        styles.commandButton,
        { borderColor: command.color },
        isRTL && { flexDirection: 'row-reverse' }
      ]}
      onPress={() => onCommandPress(command.command)}
      activeOpacity={0.7}
    >
      <View style={[styles.iconContainer, { backgroundColor: command.color }]}>
        <Ionicons
          name={command.icon as any}
          size={20}
          color={colors.white}
        />
      </View>
      <Text style={[styles.commandText, isRTL && { textAlign: 'right' }]}>
        {command.title}
      </Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <Text style={[styles.title, isRTL && { textAlign: 'right' }]}>
        {t('ai.quickCommands', 'Quick Commands')}
      </Text>
      <Text style={[styles.subtitle, isRTL && { textAlign: 'right' }]}>
        {t('ai.tapToUse', 'Tap to use')}
      </Text>
      
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={[
          styles.commandsContainer,
          isRTL && { flexDirection: 'row-reverse' }
        ]}
      >
        {quickCommands.map(renderCommand)}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
    paddingHorizontal: 4,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 12,
    color: colors.text.secondary,
    marginBottom: 12,
  },
  commandsContainer: {
    paddingHorizontal: 4,
    gap: 12,
  },
  commandButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderWidth: 1,
    minWidth: 140,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  iconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  commandText: {
    fontSize: 13,
    fontWeight: '500',
    color: colors.text.primary,
    flex: 1,
  },
});
