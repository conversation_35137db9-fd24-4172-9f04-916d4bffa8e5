import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  SafeAreaView,
  Alert,
} from 'react-native';
import { Stack, useLocalSearchParams, router } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import { useTranslation } from '@/i18n/useTranslation';
import {
  ArrowLeft,
  Calendar,
  Clock,
  Edit,
  Trash2,
  Tractor,
  Wrench,
  DollarSign,
  MapPin,
  Package,
  AlertCircle,
  CheckCircle,
  AlertTriangle,
  User,
  FileText,
  History,
} from 'lucide-react-native';

export default function InventoryEquipmentDetailScreen() {
  const { id } = useLocalSearchParams();
  const { t, isRTL } = useTranslation();
  const { user } = useAuthStore();
  const { inventoryEquipment, currentFarm, isLoading } = useFarmStore();
  
  const [equipmentData, setEquipmentData] = useState<any>(null);

  useEffect(() => {
    if (id && inventoryEquipment) {
      const item = inventoryEquipment.find(eq => eq.id === id);
      setEquipmentData(item);
    }
  }, [id, inventoryEquipment]);

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Equipment':
        return <Tractor size={24} color={colors.secondary} />;
      case 'Tools':
        return <Wrench size={24} color={colors.primary} />;
      default:
        return <Package size={24} color={colors.gray[500]} />;
    }
  };

  const getStatusColor = () => {
    if (!equipmentData) return colors.gray[500];
    
    if (equipmentData.quantity <= (equipmentData.minQuantity || 0)) {
      return colors.danger;
    }
    if (equipmentData.expiryDate && new Date(equipmentData.expiryDate) < new Date()) {
      return colors.warning;
    }
    return colors.success;
  };

  const getStatusText = () => {
    if (!equipmentData) return '';
    
    if (equipmentData.quantity <= (equipmentData.minQuantity || 0)) {
      return t('equipment.status.lowStock', 'Low Stock');
    }
    if (equipmentData.expiryDate && new Date(equipmentData.expiryDate) < new Date()) {
      return t('equipment.status.expired', 'Expired');
    }
    return t('equipment.status.available', 'Available');
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  const renderDetailRow = (icon: React.ReactNode, label: string, value: string | number) => (
    <View style={[styles.detailRow, isRTL && { flexDirection: 'row-reverse' }]}>
      <View style={styles.detailIcon}>
        {icon}
      </View>
      <View style={[styles.detailContent, isRTL && { alignItems: 'flex-end' }]}>
        <Text style={[styles.detailLabel, isRTL && { textAlign: 'right' }]}>{label}</Text>
        <Text style={[styles.detailValue, isRTL && { textAlign: 'right' }]}>{value}</Text>
      </View>
    </View>
  );

  const renderHistoryItem = (historyItem: any, index: number) => (
    <View key={index} style={styles.historyItem}>
      <View style={styles.historyDot} />
      <View style={styles.historyContent}>
        <Text style={styles.historyDetails}>{historyItem.details}</Text>
        <Text style={styles.historyDate}>
          {formatDate(historyItem.date)}
        </Text>
      </View>
    </View>
  );

  if (isLoading || !equipmentData) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen
          options={{
            title: t('equipment.details', 'Equipment Details'),
            headerLeft: () => (
              <TouchableOpacity onPress={() => router.back()}>
                <ArrowLeft size={24} color={colors.primary} />
              </TouchableOpacity>
            ),
          }}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>
            {t('common.loading', 'Loading...')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen
        options={{
          title: equipmentData.name,
          headerLeft: () => (
            <TouchableOpacity onPress={() => router.back()}>
              <ArrowLeft size={24} color={colors.primary} />
            </TouchableOpacity>
          ),
          headerRight: () => (
            <View style={styles.headerButtons}>
              <TouchableOpacity style={styles.headerButton}>
                <Edit size={20} color={colors.primary} />
              </TouchableOpacity>
            </View>
          ),
        }}
      />

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header Image */}
        <View style={styles.imageContainer}>
          {equipmentData.imageUrl ? (
            <Image
              source={{ uri: equipmentData.imageUrl }}
              style={styles.image}
              resizeMode="cover"
            />
          ) : (
            <View style={styles.imagePlaceholder}>
              {getCategoryIcon(equipmentData.category)}
            </View>
          )}
        </View>

        {/* Basic Info */}
        <View style={styles.infoContainer}>
          <View style={[styles.header, isRTL && { alignItems: 'flex-end' }]}>
            <Text style={[styles.name, isRTL && { textAlign: 'right' }]}>
              {equipmentData.name}
            </Text>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor() + '20' }]}>
              <Text style={[styles.statusText, { color: getStatusColor() }]}>
                {getStatusText()}
              </Text>
            </View>
          </View>

          <View style={[styles.categoryRow, isRTL && { flexDirection: 'row-reverse' }]}>
            {getCategoryIcon(equipmentData.category)}
            <Text style={[styles.categoryText, isRTL && { marginRight: 8, marginLeft: 0 }]}>
              {equipmentData.category}
            </Text>
            {equipmentData.isConsumable && (
              <View style={styles.consumableBadge}>
                <Text style={styles.consumableBadgeText}>
                  {t('equipment.consumable', 'Consumable')}
                </Text>
              </View>
            )}
          </View>

          {equipmentData.description && equipmentData.description !== 'No description' && (
            <Text style={[styles.description, isRTL && { textAlign: 'right' }]}>
              {equipmentData.description}
            </Text>
          )}
        </View>

        {/* Details Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, isRTL && { textAlign: 'right' }]}>
            {t('equipment.details', 'Details')}
          </Text>

          {renderDetailRow(
            <Package size={20} color={colors.gray[600]} />,
            t('equipment.quantity', 'Quantity'),
            `${equipmentData.quantity} ${equipmentData.unit}`
          )}

          {equipmentData.price && renderDetailRow(
            <DollarSign size={20} color={colors.gray[600]} />,
            t('equipment.price', 'Price'),
            `$${equipmentData.price}`
          )}

          {equipmentData.supplier && renderDetailRow(
            <User size={20} color={colors.gray[600]} />,
            t('equipment.supplier', 'Supplier'),
            equipmentData.supplier
          )}

          {equipmentData.purchaseDate && renderDetailRow(
            <Calendar size={20} color={colors.gray[600]} />,
            t('equipment.purchaseDate', 'Purchase Date'),
            formatDate(equipmentData.purchaseDate)
          )}

          {equipmentData.expiryDate && renderDetailRow(
            <Clock size={20} color={colors.gray[600]} />,
            t('equipment.expiryDate', 'Expiry Date'),
            formatDate(equipmentData.expiryDate)
          )}

          {equipmentData.minQuantity && renderDetailRow(
            <AlertTriangle size={20} color={colors.gray[600]} />,
            t('equipment.minQuantity', 'Minimum Quantity'),
            `${equipmentData.minQuantity} ${equipmentData.unit}`
          )}
        </View>

        {/* History Section */}
        {equipmentData.history && equipmentData.history.length > 0 && (
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, isRTL && { textAlign: 'right' }]}>
              {t('equipment.history', 'History')}
            </Text>
            <View style={styles.historyContainer}>
              {equipmentData.history.map((item: any, index: number) => 
                renderHistoryItem(item, index)
              )}
            </View>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.gray[600],
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    padding: 8,
    marginLeft: 8,
  },
  scrollView: {
    flex: 1,
  },
  imageContainer: {
    height: 200,
    backgroundColor: colors.white,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  imagePlaceholder: {
    width: '100%',
    height: '100%',
    backgroundColor: colors.gray[100],
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoContainer: {
    backgroundColor: colors.white,
    padding: 16,
    marginBottom: 12,
  },
  header: {
    marginBottom: 12,
  },
  name: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.gray[800],
    marginBottom: 8,
  },
  statusBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 6,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  categoryRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  categoryText: {
    fontSize: 16,
    color: colors.gray[700],
    marginLeft: 8,
    fontWeight: '500',
  },
  consumableBadge: {
    backgroundColor: colors.info + '20',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    marginLeft: 12,
  },
  consumableBadgeText: {
    fontSize: 11,
    color: colors.info,
    fontWeight: '500',
  },
  description: {
    fontSize: 14,
    color: colors.gray[600],
    lineHeight: 20,
  },
  section: {
    backgroundColor: colors.white,
    padding: 16,
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[100],
  },
  detailIcon: {
    width: 40,
    alignItems: 'center',
  },
  detailContent: {
    flex: 1,
    marginLeft: 12,
  },
  detailLabel: {
    fontSize: 12,
    color: colors.gray[500],
    marginBottom: 2,
  },
  detailValue: {
    fontSize: 16,
    color: colors.gray[800],
    fontWeight: '500',
  },
  historyContainer: {
    paddingLeft: 20,
  },
  historyItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  historyDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.primary,
    marginTop: 6,
    marginRight: 12,
  },
  historyContent: {
    flex: 1,
  },
  historyDetails: {
    fontSize: 14,
    color: colors.gray[800],
    marginBottom: 4,
  },
  historyDate: {
    fontSize: 12,
    color: colors.gray[500],
  },
});
