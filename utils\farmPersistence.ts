import AsyncStorage from '@react-native-async-storage/async-storage';

const SELECTED_FARM_KEY = '@selected_farm_id';
const FARM_SELECTION_TIMESTAMP_KEY = '@farm_selection_timestamp';

export interface PersistedFarmData {
  farmId: string;
  farmName: string;
  timestamp: number;
}

/**
 * Save the selected farm to AsyncStorage
 */
export const saveSelectedFarm = async (farmId: string, farmName: string): Promise<void> => {
  try {
    const farmData: PersistedFarmData = {
      farmId,
      farmName,
      timestamp: Date.now(),
    };
    
    await AsyncStorage.setItem(SELECTED_FARM_KEY, JSON.stringify(farmData));
    console.log('Farm selection saved:', farmData);
  } catch (error) {
    console.error('Error saving selected farm:', error);
  }
};

/**
 * Get the selected farm from AsyncStorage
 */
export const getSelectedFarm = async (): Promise<PersistedFarmData | null> => {
  try {
    const farmDataString = await AsyncStorage.getItem(SELECTED_FARM_KEY);
    
    if (!farmDataString) {
      return null;
    }
    
    const farmData: PersistedFarmData = JSON.parse(farmDataString);
    
    // Check if the selection is not too old (optional: expire after 30 days)
    const thirtyDaysInMs = 30 * 24 * 60 * 60 * 1000;
    const isExpired = Date.now() - farmData.timestamp > thirtyDaysInMs;
    
    if (isExpired) {
      await clearSelectedFarm();
      return null;
    }
    
    return farmData;
  } catch (error) {
    console.error('Error getting selected farm:', error);
    return null;
  }
};

/**
 * Clear the selected farm from AsyncStorage
 */
export const clearSelectedFarm = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem(SELECTED_FARM_KEY);
    console.log('Farm selection cleared');
  } catch (error) {
    console.error('Error clearing selected farm:', error);
  }
};

/**
 * Check if a farm selection exists
 */
export const hasSelectedFarm = async (): Promise<boolean> => {
  try {
    const farmData = await getSelectedFarm();
    return farmData !== null;
  } catch (error) {
    console.error('Error checking selected farm:', error);
    return false;
  }
};

/**
 * Update the timestamp of the current selection (to prevent expiration)
 */
export const refreshFarmSelection = async (): Promise<void> => {
  try {
    const currentFarm = await getSelectedFarm();
    if (currentFarm) {
      await saveSelectedFarm(currentFarm.farmId, currentFarm.farmName);
    }
  } catch (error) {
    console.error('Error refreshing farm selection:', error);
  }
};
