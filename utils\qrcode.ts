import { nanoid } from 'nanoid/non-secure';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import * as MediaLibrary from 'expo-media-library';
import { Alert, Platform } from 'react-native';

/**
 * Generates a unique ID for an item with a specific prefix
 * @param prefix The prefix for the ID (e.g., 'PLANT', 'ANIMAL')
 * @returns A unique ID with the format PREFIX-XXXX
 */
export const generateUniqueItemId = (prefix: string): string => {
  // Generate a random 4-digit number
  const randomNum = Math.floor(1000 + Math.random() * 9000);
  return `${prefix.toUpperCase()}-${randomNum}`;
};

/**
 * Generates a suggested ID for an item based on its type
 * @param itemType The type of item (e.g., 'plant', 'animal', 'field')
 * @param itemName Optional item name to incorporate into the ID
 * @returns A suggested ID with the format TYPE-XXXX
 */
export const generateSuggestedItemId = (itemType: string, itemName?: string): string => {
  const prefixMap: Record<string, string> = {
    plant: 'PLT',
    animal: 'ANM',
    field: 'FLD',
    garden: 'GRD',
    equipment: 'EQP',
    task: 'TSK',
    yield: 'YLD',
    inventory: 'INV',
    farm: 'FRM',
  };

  const prefix = prefixMap[itemType.toLowerCase()] || itemType.substring(0, 3).toUpperCase();
  return generateUniqueItemId(prefix);
};

/**
 * Validates an item ID to ensure it follows the correct format
 * @param id The ID to validate
 * @returns True if the ID is valid, false otherwise
 */
export const validateItemId = (id: string): boolean => {
  // Check if the ID follows the format PREFIX-XXXX
  const regex = /^[A-Z]{2,5}-\d{4}$/;
  return regex.test(id);
};

/**
 * Generates a universal link for a QR code
 * @param itemType The type of item (e.g., 'plant', 'animal', 'field')
 * @param itemId The ID of the item
 * @param farmId The ID of the farm
 * @returns A universal link for the QR code
 */
export const generateUniversalLink = (
  itemType: string,
  itemId: string,
  farmId: string
): string => {
  // Create a universal link that can be used in QR codes
  // This link should be handled by the app when scanned
  // return `https://kissan-dost/${itemType}/${itemId}?farm=${farmId}`;
  return `https://com.kissandost.app/${itemType}/${itemId}?farm=${farmId}`;
};

/**
 * Parses a universal link to extract item information
 * @param link The universal link to parse
 * @returns Parsed link data or null if invalid
 */
export const parseUniversalLink = (link: string): {
  itemType: string;
  itemId: string;
  farmId: string;
} | null => {
  try {
    // Handle both possible URL formats
    const urlPattern = /https:\/\/(?:kissan-dost|com\.kissandost\.app)\/([^\/]+)\/([^?]+)\?farm=([^&]+)/;
    const match = link.match(urlPattern);
    
    if (match) {
      return {
        itemType: match[1],
        itemId: match[2],
        farmId: match[3]
      };
    }
    
    return null;
  } catch (error) {
    console.error('Error parsing universal link:', error);
    return null;
  }
};

/**
 * Saves a QR code to the device's media library
 * @param qrRef Reference to the QR code component
 * @returns A promise that resolves to true if successful, false otherwise
 */
export const saveQRCode = async (qrRef: any): Promise<boolean> => {
  try {
    if (Platform.OS === 'web') {
      console.log('Saving QR codes is not supported on web');
      return false;
    }

    // Request permission to save to media library
    const { status } = await MediaLibrary.requestPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission Denied', 'Please grant permission to save the QR code');
      return false;
    }

    // Get the QR code as a base64 string
    const qrCode = await qrRef.toDataURL();
    
    // Create a temporary file path
    const fileUri = FileSystem.documentDirectory + 'qrcode.png';
    
    // Write the base64 data to a file
    await FileSystem.writeAsStringAsync(
      fileUri,
      qrCode.replace('data:image/png;base64,', ''),
      { encoding: FileSystem.EncodingType.Base64 }
    );
    
    // Save the file to the media library
    await MediaLibrary.saveToLibraryAsync(fileUri);
    
    // Delete the temporary file
    await FileSystem.deleteAsync(fileUri);
    
    return true;
  } catch (error) {
    console.error('Error saving QR code:', error);
    return false;
  }
};

/**
 * Shares a QR code with other apps
 * @param qrRef Reference to the QR code component
 * @param itemType The type of item (e.g., 'plant', 'animal', 'field')
 * @param itemName The name of the item
 * @returns A promise that resolves to true if successful, false otherwise
 */
export const shareQRCode = async (
  qrRef: any,
  itemType: string = 'item',
  itemName: string = 'Item'
): Promise<boolean> => {
  try {
    if (Platform.OS === 'web') {
      console.log('Sharing QR codes is not supported on web');
      return false;
    }

    // Check if sharing is available
    const isAvailable = await Sharing.isAvailableAsync();
    if (!isAvailable) {
      Alert.alert('Sharing not available', 'Sharing is not available on this device');
      return false;
    }

    // Get the QR code as a base64 string
    const qrCode = await qrRef.toDataURL();
    
    // Create a temporary file path
    const fileUri = FileSystem.documentDirectory + 'qrcode.png';
    
    // Write the base64 data to a file
    await FileSystem.writeAsStringAsync(
      fileUri,
      qrCode.replace('data:image/png;base64,', ''),
      { encoding: FileSystem.EncodingType.Base64 }
    );
    
    // Share the file
    await Sharing.shareAsync(fileUri, {
      mimeType: 'image/png',
      dialogTitle: `Share ${itemType} QR Code: ${itemName}`,
      UTI: 'public.png'
    });
    
    // Delete the temporary file
    await FileSystem.deleteAsync(fileUri);
    
    return true;
  } catch (error) {
    console.error('Error sharing QR code:', error);
    return false;
  }
};

/**
 * Generates a QR code data URL for an item
 * @param itemType The type of item (e.g., 'plant', 'animal', 'field')
 * @param itemId The ID of the item
 * @param farmId The ID of the farm
 * @returns A promise that resolves to a data URL for the QR code
 */
export const generateQRCodeDataURL = async (
  itemType: string,
  itemId: string,
  farmId: string
): Promise<string> => {
  try {
    const QRCode = await import('qrcode');
    const link = generateUniversalLink(itemType, itemId, farmId);
    
    return await QRCode.toDataURL(link, {
      errorCorrectionLevel: 'H',
      margin: 1,
      width: 200,
      color: {
        dark: '#000000',
        light: '#FFFFFF',
      },
    });
  } catch (error) {
    console.error('Error generating QR code:', error);
    throw error;
  }
};

