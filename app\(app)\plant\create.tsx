import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  Modal,
  FlatList,
  ActivityIndicator,
  Switch,
} from 'react-native';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import Button from '@/components/Button';
import Input from '@/components/Input';
import ImagePicker from '@/components/ImagePicker';
import MultipleImagePicker from '@/components/MultipleImagePicker';
import DatePicker from '@/components/DatePicker';
import Dropdown, { DropdownOption } from '@/components/Dropdown';
import EnhancedDropdown from '@/components/EnhancedDropdown';
import { uploadImageAsync } from '@/utils/firebase-storage';
import {
  ChevronDown,
  Ruler,
  Home,
  Check,
  Heart,
  QrCode,
  Sprout,
  TreeDeciduous,
  Leaf,
  Map,
  MapPin,
} from 'lucide-react-native';
import { Field, Garden, Plant } from '@/types';
import { analyzeImageWithVision, getPlantCareAdvice } from '@/utils/openai-vision';
import { generateUniqueItemId, validateItemId, generateUniversalLink } from '@/utils/qrcode';
import { v4 as uuidv4 } from 'uuid';
import { useAuthStore } from '@/store/auth-store';
import DropdownPicker from '@/components/DropdownPicker';
import { MaterialIcons } from '@expo/vector-icons';
import { useTranslation } from '@/i18n/useTranslation';
import { analyzeImageWithAI, analyzeImageForFormPopulation } from '@/utils/ai-utils';
import { capitalizeFirstLetter } from '@/utils/util';
import { useLookupStore } from '@/store/lookup-store';
import Toast from 'react-native-toast-message';

export default function CreatePlantScreen() {
  const { addPlant, gardens, fields, farms, getPlantUpdated, fetchGardens, fetchFarms, fetchFields, currentFarm, plants, updatePlant, getPlant } = useFarmStore();
  const { user } = useAuthStore();
  // getLookupsByCategory
  const { getLookupsByCategory, getLookupsByCategoryParssedData } = useLookupStore();
  const { id } = useLocalSearchParams();
  // console.log({id})
  const isEditMode = id ? true : false;
  const { t, isRTL } = useTranslation()
  const [name, setName] = useState('');
  const [species, setSpecies] = useState('');
  const [variety, setVariety] = useState('');
  const [plantedDate, setPlantedDate] = useState(new Date());
  const [expectedHarvestDate, setExpectedHarvestDate] = useState<Date | null>(null);
  const [status, setStatus] = useState<'seedling' | 'growing' | 'flowering' | 'fruiting' | 'harvested' | 'dormant'>('seedling');
  const [health, setHealth] = useState<'excellent' | 'good' | 'fair' | 'poor'>('good');
  const [gardenId, setGardenId] = useState('');
  const [gardenName, setGardenName] = useState('');
  const [fieldId, setFieldId] = useState('');
  const [fieldName, setFieldName] = useState('');
  const [notes, setNotes] = useState('');
  const [images, setImages] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // New field for Item ID
  const [itemId, setItemId] = useState(''); // Kept for edit mode
  const [isMultiple, setIsMultiple] = useState(false);
  const [quantity, setQuantity] = useState('1');

  const [showGardenModal, setShowGardenModal] = useState(false);
  const [showFieldModal, setShowFieldModal] = useState(false);
  const [analyzingImage, setAnalyzingImage] = useState(false);

  // Field validation errors
  const [fieldErrors, setFieldErrors] = useState({
    name: '',
    species: '',
    variety: '',
    plantedDate: '',
    images: ''
  });
  const statusOptions: DropdownOption[] = [
    {
      label: t('form.seedling'),
      value: 'seedling',
      icon: <MaterialIcons name="eco" size={24} color="black" />,
    },
    {
      label: t('form.growing'),
      value: 'growing',
      icon: <MaterialIcons name="local-florist" size={24} color="black" />,
    },
    {
      label: t('form.flowering'),
      value: 'flowering',
      icon: <MaterialIcons name="filter-vintage" size={24} color="black" />,
    },
    {
      label: t('form.fruiting'),
      value: 'fruiting',
      icon: <MaterialIcons name="emoji-nature" size={24} color="black" />,
    },
    {
      label: t('form.harvested'),
      value: 'harvested',
      icon: <MaterialIcons name="grass" size={24} color="black" />,
    },
    {
      label: t('form.dormant'),
      value: 'dormant',
      icon: <MaterialIcons name="nightlight-round" size={24} color="black" />,
    },
  ];

  const healthOptions: DropdownOption[] = [
    {
      label: t('form.excellent'),
      value: 'excellent',
      icon: <MaterialIcons name="favorite" size={20} color={colors.success} />,
      color: colors.success,
    },
    {
      label: t('form.good'),
      value: 'good',
      icon: <MaterialIcons name="favorite" size={20} color={colors.primary} />,
      color: colors.primary,
    },
    {
      label: t('form.fair'),
      value: 'fair',
      icon: <MaterialIcons name="favorite" size={20} color={colors.warning} />,
      color: colors.warning,
    },
    {
      label: t('form.poor'),
      value: 'poor',
      icon: <MaterialIcons name="favorite" size={20} color={colors.danger} />,
      color: colors.danger,
    },
  ];
  const [plantStatusArray, setPlantStatusArray] = useState([] as any)
  const [plantHealthStatusArray, setPlantHealthStatusArray] = useState([] as any)

  useEffect(() => {
    setPlantStatusArray(getLookupsByCategoryParssedData('plantStatus', 'form.'))//plantStatusArray.map((item)=>({label:item?.title ,value:item?.id})))
    setPlantHealthStatusArray(getLookupsByCategoryParssedData('plantHealthStatus', 'form.'))//plantHealthStatusArray.map((item)=>({label:item?.title ,value:item?.id})))
  }, [])

  useEffect(() => {
    if (currentFarm?.id) {
      fetchGardens(currentFarm.id);
      fetchFields(currentFarm.id);
    }
  }, [currentFarm, fetchGardens, fetchFields]);

  const [plant, setPlant] = useState({} as any)
  const loadPlantData = async (plantIdString: string) => {
    if (currentFarm?.id) {
      const res = await getPlantUpdated(currentFarm.id, plantIdString)
      setPlant(res)
    }
  }
  useEffect(() => {
    if (plant) {
      // console.log({ plant })
      setName(plant.name || '');
      setSpecies(plant.species || '');
      setVariety(plant.variety || '');
      setPlantedDate(plant.plantedDate ? new Date(plant.plantedDate) : new Date());
      setExpectedHarvestDate(plant.expectedHarvestDate ? new Date(plant.expectedHarvestDate) : null);
      setStatus(plant.status || 'seedling');
      setHealth(plant.health || 'good');
      setNotes(plant.notes || '');
      // setItemId(plant.itemId || '');
      setItemId(plant?.identificationID || "")
      // Set garden or field if available
      if (plant.gardenId) {
        setGardenId(plant.gardenId);
        const garden = gardens.find(g => g.id === plant.gardenId);
        if (garden) setGardenName(garden.name);
      }

      if (plant.fieldId) {
        setFieldId(plant.fieldId);
        const field = fields.find(f => f.id === plant.fieldId);
        if (field) setFieldName(field.name);
      }

      // Set images if available
      if (plant.image) {
        setImages([plant.image]);
      } else if (plant.images && plant.images.length > 0) {
        setImages(plant.images);
      }
    }
  }, [plant])
  useEffect(() => {
    if (isEditMode && id) {
      const plantIdString = Array.isArray(id) ? id[0] : id as string;
      loadPlantData(plantIdString);


    }
  }, [isEditMode, id, gardens, fields]);

  const validateFields = () => {
    const errors = {
      name: '',
      species: '',
      variety: '',
      plantedDate: '',
      // location: '',
      images: ''
    };

    let hasErrors = false;

    if (!name.trim()) {
      errors.name = t('plant.nameRequired');
      hasErrors = true;
    }

    if (!species.trim()) {
      errors.species = t('plant.speciesRequired');
      hasErrors = true;
    }

    // Variety is optional - no validation needed
    // Planted date is optional - no validation needed
    // Location is optional - no validation needed
    // Images are optional - no validation needed

    setFieldErrors(errors);
    return !hasErrors;
  };

  const handleCreatePlant = async () => {
    if (!validateFields()) {
      Toast.show({
        type: 'overlay',
        text1: t('common.error'),
        text2: t('common.fillAllFields'),
      });
      return;
    }

    try {
      setIsLoading(true);

      // Upload images if selected
      let imageUrls: string[] = [];
      if (images.length > 0) {
        for (const imageUri of images) {
          if (imageUri && !imageUri.startsWith('http')) {
            const uploadedUrl = await uploadImageAsync(imageUri, 'plants');
            imageUrls.push(uploadedUrl);
          } else if (imageUri) {
            imageUrls.push(imageUri);
          }
        }
      }

      // Use first image as main image
      const mainImage = imageUrls.length > 0 ? imageUrls[0] : '';

      const helpGuide=await getPlantCareAdvice(species, variety);

      const basePlantData: any = {
        name,
        species,
        plantedDate: plantedDate.toISOString(),
        status,
        health,
        farmId: currentFarm?.id,
        updatedAt: new Date(),
        helpGuide,
      };

      // Only add these fields if they have values
      if (variety) basePlantData.variety = variety;
      if (expectedHarvestDate) basePlantData.expectedHarvestDate = expectedHarvestDate.toISOString();
      if (gardenId && gardenId.trim() !== '') basePlantData.gardenId = gardenId;
      if (fieldId && fieldId.trim() !== '') basePlantData.fieldId = fieldId;
      if (notes) basePlantData.notes = notes;
      if (mainImage) basePlantData.image = mainImage;
      if (imageUrls.length > 0) basePlantData.images = imageUrls;

      if (isEditMode) {
        const updatedPlantData = {
          ...basePlantData,
          id: id as string,
          identificationID: itemId,
          qrCodeLink: plant.qrCodeLink || generateUniversalLink('plant', itemId, currentFarm?.id || ''),
        };
        await updatePlant(updatedPlantData?.id, updatedPlantData);
        Alert.alert('Success', 'Plant updated successfully', [
          { text: 'OK', onPress: () => router.back() }
        ]);
      } else {
        const numQuantity = isMultiple ? parseInt(quantity, 10) : 1;
        for (let i = 0; i < numQuantity; i++) {
          const newPlantId = uuidv4();
          const newPlantItemId = generateUniqueItemId('PLT');

          // Add numbering to plant name for multiple plants
          const plantName = numQuantity > 1 ? `${name} ${i + 1}` : name;

          const newPlantData = {
            ...basePlantData,
            name: plantName,
            id: newPlantId,
            identificationID: newPlantItemId,
            qrCodeLink: generateUniversalLink('plant', newPlantItemId, currentFarm?.id || ''),
            createdAt: new Date(),
            photos: imageUrls.length > 0 ? imageUrls.map(url => ({ url, timestamp: new Date(), takenBy: '' })) : [],
          };
          await addPlant(newPlantData);
        }

        Alert.alert(
          'Success',
          `${numQuantity} plant${numQuantity > 1 ? 's' : ''} added successfully`,
          [{ text: 'OK', onPress: () => router.back() }]
        );
      }
    } catch (error) {
      console.error(`Error ${isEditMode ? 'updating' : 'creating'} plant:`, error);
      Alert.alert('Error', `Failed to ${isEditMode ? 'update' : 'add'} plant. Please try again.`);
    } finally {
      setIsLoading(false);
    }
  };

  const renderGardenItem = ({ item }: { item: Garden }) => (
    <TouchableOpacity
      style={styles.modalItem}
      onPress={() => {
        setGardenId(item.id);
        setGardenName(item.name);
        setFieldId('');
        setFieldName('');
        setShowGardenModal(false);
      }}
    >
      <View style={styles.modalItemContent}>
        <TreeDeciduous size={20} color={colors.gray[600]} style={styles.modalItemIcon} />
        <Text style={styles.modalItemText}>{item.name}</Text>
      </View>
      {gardenId === item.id && (
        <Check size={20} color={colors.primary} />
      )}
    </TouchableOpacity>
  );

  const renderFieldItem = ({ item }: { item: Field }) => (
    <TouchableOpacity
      style={styles.modalItem}
      onPress={() => {
        setFieldId(item.id);
        setFieldName(item.name);
        setGardenId('');
        setGardenName('');
        setShowFieldModal(false);
      }}
    >
      <View style={styles.modalItemContent}>
        <Leaf size={20} color={colors.gray[600]} style={styles.modalItemIcon} />
        <Text style={styles.modalItemText}>{item.name}</Text>
      </View>
      {fieldId === item.id && (
        <Check size={20} color={colors.primary} />
      )}
    </TouchableOpacity>
  );

  const handleImageAnalysis = async (imageUri: string) => {
    try {
      setAnalyzingImage(true);
      const result = await analyzeImageWithAI(imageUri, 'plant');
console.log({result})
      if (result.details) {
        const fields = result.details;

        // Auto-populate form fields based on AI analysis
        if (fields.species && !species) {
          setSpecies(fields.species);
        }
        if (fields.variety && !variety) {
          setVariety(fields.variety);
        }
        if (fields.health && fields.health !== 'unknown') {
          setHealth(fields.health);
        }
        if (fields.growth_stage && fields.growth_stage !== 'unknown') {
          setStatus(fields.growth_stage);
        }

        // Add analysis to notes
        const analysisNotes = [
          result.analysis,
          fields.care_notes && `Care Notes: ${fields.care_notes}`,
          fields.estimated_age && `Estimated Age: ${fields.estimated_age}`,
        ].filter(Boolean).join('\n\n');

        if (analysisNotes && !notes) {
          setNotes(analysisNotes);
        }

        Alert.alert(
          'Analysis Complete',
          'Form fields have been auto-populated based on image analysis. Please review and adjust as needed.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error analyzing image:', error);
      Alert.alert('Analysis Failed', 'Could not analyze the image. Please try again.');
    } finally {
      setAnalyzingImage(false);
    }
  };

  const handleAnalysisComplete = (response: any) => {
    // console.log({ response });

    if (response && response.details) {
      const details = response.details;

      // Set species if available
      if (details.species) {
        setSpecies(details.species);
      } else if (details.plantType || details.plant_type) {
        setSpecies(details.plantType);
      }

      // Set variety if available
      if (details.variety) {
        setVariety(details.variety);
      }

      // Set growth stage if available
      if (details.growthStage) {
        const stage = details.growthStage.toLowerCase();
        if (stage.includes('seedling') || stage.includes('germination')) {
          setStatus('seedling');
        } else if (stage.includes('vegetative') || stage.includes('growing')) {
          setStatus('growing');
        } else if (stage.includes('flowering')) {
          setStatus('flowering');
        } else if (stage.includes('fruiting') || stage.includes('harvest')) {
          setStatus('fruiting');
        } else if (stage.includes('dormant')) {
          setStatus('dormant');
        }
      }

      // Set health status if available
      if (details.healthStatus) {
        const health = details.healthStatus.toLowerCase();
        if (health.includes('excellent')) {
          setHealth('excellent');
        } else if (health.includes('good') || health.includes('healthy')) {
          setHealth('good');
        } else if (health.includes('fair') || health.includes('stressed')) {
          setHealth('fair');
        } else if (health.includes('poor') || health.includes('disease') || health.includes('infected') || health.includes('pest') || health.includes('nutrient')) {
          setHealth('poor');
        }
      }

      // Add analysis to notes if empty
      if (!notes && response.analysis) {
        setNotes(response.analysis);

        // Add detailed information to notes
        const detailsText = [];
        if (details.species) detailsText.push(`Species: ${details.species}`);
        if (details.variety) detailsText.push(`Variety: ${details.variety}`);
        if (details.growthStage) detailsText.push(`Growth Stage: ${details.growthStage}`);
        if (details.healthStatus) detailsText.push(`Health Status: ${details.healthStatus}`);
        if (details.issues) detailsText.push(`Issues: ${details.issues}`);
        if (details.soilCondition) detailsText.push(`Soil Condition: ${details.soilCondition}`);
        if (details.notes) detailsText.push(`Additional Notes: ${details.notes}`);

        if (detailsText.length > 0) {
          setNotes(response.analysis + '\n\n' + detailsText.join('\n'));
        }
      }
    }
  };
  return (
    <>
      <Stack.Screen
        options={{
          title: isEditMode ? t('entity.plant.edit') : t('entity.plant.add'),
          headerShown: true,
        }}
      />

      <SafeAreaView style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <MultipleImagePicker
            label={t('plant.images')}
            images={images}
            onImagesChange={(newImages) => {
              setImages(newImages);
              if (fieldErrors.images) {
                setFieldErrors(prev => ({ ...prev, images: '' }));
              }
            }}
            maxImages={3}
            placeholder={t('plant.addPhoto')}
            error={fieldErrors.images}
            showAnalyzeButton={true}
            onAnalyzeImage={handleImageAnalysis}
            analyzingImage={analyzingImage}
          />


          <View style={styles.formContainer}>
            {!isEditMode && (
              <>
                <View style={[styles.switchContainer, isRTL && { flexDirection: 'row-reverse' }]}>
                  <Text style={styles.switchLabel}>{t('form.addMultiplePlants', 'Add multiple plants')}</Text>
                  <Switch
                    value={isMultiple}
                    onValueChange={setIsMultiple}
                    trackColor={{ false: colors.gray[300], true: colors.primaryLight }}
                    thumbColor={isMultiple ? colors.primary : colors.gray[100]}
                  />
                </View>

                {isMultiple && (
                  <Input
                    label={t('form.quantity', 'Quantity')}
                    placeholder={t('form.quantityPlaceholder', 'Enter number of plants')}
                    value={quantity}
                    onChangeText={setQuantity}
                    keyboardType="numeric"
                    containerStyle={styles.inputContainer}
                    inputStyle={{ textAlign: isRTL ? 'right' : 'left' }}
                    leftIcon={
                      isRTL ? <MaterialIcons name="format-list-numbered" size={20} color={colors.gray[500]} /> : undefined
                    }
                    rightIcon={
                      !isRTL ? <MaterialIcons name="format-list-numbered" size={20} color={colors.gray[500]} /> : undefined
                    }
                  />
                )}
              </>
            )}
            {/* <View style={styles.itemIdContainer}> */}
            {/* <View
                style={[
                  styles.itemIdHeader,
                  { flexDirection: isRTL ? 'row-reverse' : 'row' }
                ]}
              >
                <QrCode size={20} color={colors.primary} />
                <Text
                  style={[
                    styles.itemIdLabel,
                    { textAlign: isRTL ? 'right' : 'left' }
                  ]}
                >
                  {t('form.itemId')}
                </Text>
              </View> */}

            {/* <Input
                placeholder={t('form.itemIdPlaceholder')}
                value={itemId}
                onChangeText={setItemId}
                containerStyle={[
                  styles.itemIdInput,
                  !isItemIdValid && styles.itemIdInputError
                ]}
                inputStyle={{ textAlign: isRTL ? 'right' : 'left' }}
                required
              // editable={false}
              /> */}

            {/* {!isItemIdValid && itemIdError ? (
                <Text
                  style={[
                    styles.itemIdErrorText,
                    { textAlign: isRTL ? 'right' : 'left' }
                  ]}
                >
                  {itemIdError}
                </Text>
              ) : null}

              <Text
                style={[
                  styles.itemIdHelpText,
                  { textAlign: isRTL ? 'right' : 'left' }
                ]}
              >
                {t('form.itemIdHelp')}
              </Text>
            </View> */}
            {/* import {MaterialIcons} from '@expo/vector-icons'; // or 'react-native-vector-icons/MaterialIcons'

            // ... */}

            <Input
              label={t('form.plantName')}
              placeholder={t('form.plantNamePlaceholder')}
              value={name}
              onChangeText={(text) => {
                setName(text);
                if (fieldErrors.name) {
                  setFieldErrors(prev => ({ ...prev, name: '' }));
                }
              }}
              containerStyle={styles.inputContainer}
              inputStyle={{ textAlign: isRTL ? 'right' : 'left' }}
              leftIcon={
                isRTL ? <MaterialIcons name="grass" size={20} color={colors.gray[500]} /> : undefined
              }
              rightIcon={
                !isRTL ? <MaterialIcons name="grass" size={20} color={colors.gray[500]} /> : undefined
              }
              required={true}
              error={fieldErrors.name}
            />

            <Input
              label={t('form.species')}
              placeholder={t('form.speciesPlaceholder')}
              value={species}
              onChangeText={(text) => {
                setSpecies(text);
                if (fieldErrors.species) {
                  setFieldErrors(prev => ({ ...prev, species: '' }));
                }
              }}
              containerStyle={styles.inputContainer}
              inputStyle={{ textAlign: isRTL ? 'right' : 'left' }}
              leftIcon={
                isRTL ? <MaterialIcons name="eco" size={20} color={colors.gray[500]} /> : undefined
              }
              rightIcon={
                !isRTL ? <MaterialIcons name="eco" size={20} color={colors.gray[500]} /> : undefined
              }
              required={true}
              error={fieldErrors.species}
            />

            <Input
              label={t('form.variety') + ' (Optional)'}
              placeholder={t('form.varietyPlaceholder')}
              value={variety}
              onChangeText={setVariety}
              containerStyle={styles.inputContainer}
              inputStyle={{ textAlign: isRTL ? 'right' : 'left' }}
              leftIcon={
                isRTL ? <MaterialIcons name="local-florist" size={20} color={colors.gray[500]} /> : undefined
              }
              rightIcon={
                !isRTL ? <MaterialIcons name="local-florist" size={20} color={colors.gray[500]} /> : undefined
              }
              required={false}
            />

            <DatePicker
              label={t('form.plantedDate')}
              value={plantedDate}
              placeholder={t('form.selectDatePlaceholder')}
              onChange={(date) => {
                setPlantedDate(date);
                if (fieldErrors.plantedDate) {
                  setFieldErrors(prev => ({ ...prev, plantedDate: '' }));
                }
              }}
              required={true}
            // isRTL={isRTL}
            />
            {fieldErrors.plantedDate && (
              <Text style={styles.errorText}>{fieldErrors.plantedDate}</Text>
            )}

            <DatePicker
              label={t('form.expectedHarvestDate')}
              placeholder={t('form.expectedHarvestDatePlaceholder')}
              value={expectedHarvestDate}
              onChange={setExpectedHarvestDate}
              startOffset={0}
              endOffset={36}
            // isRTL={isRTL}
            />

            {/* <Text style={[styles.label, { textAlign: isRTL ? 'right' : 'left' }]}>
              {t('form.status')}
            </Text> */}

            <DropdownPicker
              label={t('form.status')}
              options={plantStatusArray}
              onSelect={(val) => setStatus(val as any)}
              selectedValue={status}
              isMultiple={false}
            // isRTL={isRTL}
            />
            {/* <Text style={[styles.label, { textAlign: isRTL ? 'right' : 'left' }]}>
              {t('form.healthStatus')}
            </Text> */}
            <DropdownPicker
              label={t('form.healthStatus')}
              options={plantHealthStatusArray}
              onSelect={(val) => setHealth(val as any)}
              selectedValue={health}
              isMultiple={false}
            // isRTL={isRTL}
            />

            <Text style={[styles.label, { textAlign: isRTL ? 'right' : 'left' }]}>
              {t('form.location')}
            </Text>
            <View style={styles.locationContainer}>
              <TouchableOpacity
                style={[
                  styles.locationButton,
                  gardenId ? styles.locationButtonActive : null,
                  isRTL && { flexDirection: 'row-reverse' }
                ]}
                onPress={() => setShowGardenModal(true)}
              >
                <MaterialIcons
                  name="park"
                  size={20}
                  color={gardenId ? colors.white : colors.gray[600]}
                />
                <Text style={[
                  styles.locationButtonText,
                  gardenId ? styles.locationButtonTextActive : null,
                  { textAlign: isRTL ? 'right' : 'left' },
                  { marginRight: isRTL ? 8 : 0 }
                ]}>
                  {gardenName || t('form.selectGarden')}
                </Text>
              </TouchableOpacity>

              <Text style={[styles.orText, { textAlign: isRTL ? 'right' : "left" }]}>{t('common.or')}</Text>

              <TouchableOpacity
                style={[
                  styles.locationButton,
                  fieldId ? styles.locationButtonActive : null,
                  isRTL && { flexDirection: 'row-reverse' }
                ]}
                onPress={() => setShowFieldModal(true)}
              >
                <MaterialIcons
                  name="agriculture"
                  size={20}
                  color={fieldId ? colors.white : colors.gray[600]}
                />
                <Text style={[
                  styles.locationButtonText,
                  fieldId ? styles.locationButtonTextActive : null,
                  { textAlign: isRTL ? 'right' : 'left' },
                  { marginRight: isRTL ? 8 : 0 }
                ]}>
                  {fieldName || t('form.selectField')}
                </Text>
              </TouchableOpacity>
            </View>

            <Input
              label={t('form.notes')}
              placeholder={t('form.notesPlaceholder')}
              value={notes}
              onChangeText={setNotes}
              multiline
              numberOfLines={4}
              containerStyle={styles.inputContainer}
              inputStyle={[styles.textArea, { textAlign: isRTL ? 'right' : 'left' }]}
            />

          </View>

          <View style={styles.buttonContainer}>
            <Button
              title={t('common.cancel')}
              variant="outline"
              onPress={() => router.back()}
              style={styles.cancelButton}
              disabled={isLoading}
            />
            <Button
              title={isLoading ? (isEditMode ? t('common.updating') : t('add.plant')): t('add.plant')}
              onPress={handleCreatePlant}
              style={styles.createButton}
              disabled={isLoading}
              leftIcon={
                isLoading
                  ? <ActivityIndicator size="small" color={colors.white} />
                  : undefined
              }
            />
          </View>
          {/* <Input
              label="Plant Name"
              placeholder="Enter plant name"
              value={name}
              onChangeText={setName}
              containerStyle={styles.inputContainer}
              leftIcon={<Sprout size={20} color={colors.gray[500]} />}
            />

            <Input
              label="Species"
              placeholder="Enter plant species"
              value={species}
              onChangeText={setSpecies}
              containerStyle={styles.inputContainer}
            />

            <Input
              label="Variety (Optional)"
              placeholder="Enter plant variety"
              value={variety}
              onChangeText={setVariety}
              containerStyle={styles.inputContainer}
            />

            <DatePicker
              label="Planted Date"
              value={plantedDate}
              onChange={setPlantedDate}
              required={true}
            />

            <DatePicker
              label="Expected Harvest Date (Optional)"
              value={expectedHarvestDate}
              onChange={setExpectedHarvestDate}
              placeholder="Select a date"
              startOffset={0}
              endOffset={36}
            />
            <Text style={styles.label}>Status</Text>
            <DropdownPicker
              label="Select Status"
              options={statusOptions}
              onSelect={(val) => setStatus(val as any)}
              selectedValue={status}
              isMultiple={false}
            /> */}

          {/* <EnhancedDropdown
              label="Status"
              placeholder="Select plant status"
              options={statusOptions}
              value={status}
              onChange={(value) => setStatus(value as any)}
              lookupCollection="dropdownLookups/plantStatuses"
              zIndex={5000}
              zIndexInverse={1000}
            /> */}
          {/* <Text style={styles.label}>Health Status</Text>
            <DropdownPicker
              label="Health Status"
              options={healthOptions}
              onSelect={(val) => setHealth(val as any)}
              selectedValue={health}
              isMultiple={false}
            />
            
            <Text style={styles.label}>Location</Text>
            <View style={styles.locationContainer}>
              <TouchableOpacity
                style={[
                  styles.locationButton,
                  gardenId ? styles.locationButtonActive : null
                ]}
                onPress={() => setShowGardenModal(true)}
              >
                <TreeDeciduous size={20} color={gardenId ? colors.white : colors.gray[600]} />
                <Text style={[
                  styles.locationButtonText,
                  gardenId ? styles.locationButtonTextActive : null
                ]}>
                  {gardenName || "Select Garden"}
                </Text>
              </TouchableOpacity>

              <Text style={styles.orText}>OR</Text>

              <TouchableOpacity
                style={[
                  styles.locationButton,
                  fieldId ? styles.locationButtonActive : null
                ]}
                onPress={() => setShowFieldModal(true)}
              >
                <Leaf size={20} color={fieldId ? colors.white : colors.gray[600]} />
                <Text style={[
                  styles.locationButtonText,
                  fieldId ? styles.locationButtonTextActive : null
                ]}>
                  {fieldName || "Select Field"}
                </Text>
              </TouchableOpacity>
            </View>

            <Input
              label="Notes (Optional)"
              placeholder="Enter any additional notes"
              value={notes}
              onChangeText={setNotes}
              multiline
              numberOfLines={4}
              containerStyle={styles.inputContainer}
              inputStyle={styles.textArea}
            />
          </View>

          <View style={styles.buttonContainer}>
            <Button
              title="Cancel"
              variant="outline"
              onPress={() => router.back()}
              style={styles.cancelButton}
              disabled={isLoading}
            />
            <Button
              title={isLoading ? "Adding..." : "Add Plant"}
              onPress={handleCreatePlant}
              style={styles.createButton}
              disabled={isLoading}
              leftIcon={isLoading ? <ActivityIndicator size="small" color={colors.white} /> : undefined}
            />
          </View> */}
        </ScrollView>

        {/* Garden Selection Modal */}
        <Modal
          visible={showGardenModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowGardenModal(false)}
        >
          <TouchableOpacity
            style={styles.modalOverlay}
            activeOpacity={1}
            onPress={() => setShowGardenModal(false)}
          >
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Select Garden</Text>
                <TouchableOpacity onPress={() => setShowGardenModal(false)}>
                  <Text style={styles.modalCloseText}>Close</Text>
                </TouchableOpacity>
              </View>
              {gardens.length > 0 ? (
                <FlatList
                  data={gardens}
                  renderItem={renderGardenItem}
                  keyExtractor={item => item.id}
                  style={styles.modalList}
                />
              ) : (
                <View style={styles.emptyListContainer}>
                  <Text style={styles.emptyListText}>No gardens available. Please create a garden first.</Text>
                </View>
              )}
            </View>
          </TouchableOpacity>
        </Modal>

        {/* Field Selection Modal */}
        <Modal
          visible={showFieldModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowFieldModal(false)}
        >
          <TouchableOpacity
            style={styles.modalOverlay}
            activeOpacity={1}
            onPress={() => setShowFieldModal(false)}
          >
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Select Field</Text>
                <TouchableOpacity onPress={() => setShowFieldModal(false)}>
                  <Text style={styles.modalCloseText}>Close</Text>
                </TouchableOpacity>
              </View>
              {fields.length > 0 ? (
                <FlatList
                  data={fields}
                  renderItem={renderFieldItem}
                  keyExtractor={item => item.id}
                  style={styles.modalList}
                />
              ) : (
                <View style={styles.emptyListContainer}>
                  <Text style={styles.emptyListText}>No fields available. Please create a field first.</Text>
                </View>
              )}
            </View>
          </TouchableOpacity>
        </Modal>
      </SafeAreaView>
    </>
  );
}

// Helper function to get color based on health status
const getHealthColor = (health: string) => {
  switch (health) {
    case 'excellent':
      return colors.success;
    case 'good':
      return colors.primary;
    case 'fair':
      return colors.warning;
    case 'poor':
      return colors.danger;
    default:
      return colors.gray[500];
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  formContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  inputContainer: {
    marginBottom: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
    paddingTop: 12,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginBottom: 8,
  },
  healthOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    paddingVertical: 4,
  },
  healthOptionText: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
  },
  locationContainer: {
    marginBottom: 16,
  },
  locationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    marginBottom: 8,
  },
  locationButtonActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  locationButtonText: {
    fontSize: 14,
    color: colors.gray[800],
    marginLeft: 8,
  },
  locationButtonTextActive: {
    color: colors.white,
  },
  orText: {
    textAlign: 'center',
    fontSize: 14,
    color: colors.gray[500],
    marginVertical: 8,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  cancelButton: {
    flex: 1,
    marginRight: 8,
  },
  createButton: {
    flex: 1,
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 20,
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
  },
  modalCloseText: {
    fontSize: 16,
    color: colors.primary,
  },
  modalList: {
    maxHeight: '80%',
  },
  modalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modalItemIcon: {
    marginRight: 12,
  },
  modalItemText: {
    fontSize: 16,
    color: colors.gray[800],
  },
  emptyListContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyListText: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
  },
  // Item ID styles
  itemIdContainer: {
    marginBottom: 16,
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    padding: 12,
  },
  itemIdHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  itemIdLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    marginLeft: 8,
  },
  itemIdInput: {
    marginBottom: 4,
  },
  itemIdInputError: {
    borderColor: colors.danger,
  },
  itemIdErrorText: {
    fontSize: 12,
    color: colors.danger,
    marginBottom: 4,
  },
  itemIdHelpText: {
    fontSize: 12,
    color: colors.gray[500],
  },
});
