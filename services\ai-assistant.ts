import { OpenAI } from 'openai';
import * as FileSystem from 'expo-file-system';
import { generateUniqueItemId, parseUniversalLink } from '@/utils/qrcode';

const OPENAI_API_KEY = '********************************************************************************************************************************************************************';

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  images?: string[];
  timestamp: Date;
  metadata?: {
    suggestedActions?: SuggestedAction[];
    analysisData?: any;
    entityList?: EntityListItem[];
    showEntityDetails?: {
      entityId: string;
      entityType: string;
      data: any;
    };
    responseType?: 'text' | 'entity_list' | 'entity_details' | 'update_form';
  };
}

export interface SuggestedAction {
  id: string;
  type: 'create' | 'update' | 'delete';
  entity: 'animal' | 'plant' | 'garden' | 'field' | 'equipment';
  title: string;
  description: string;
  data: any;
  confidence: number;
}

export interface EntityListItem {
  id: string;
  name: string;
  type: 'animal' | 'plant' | 'garden' | 'field' | 'equipment';
  status?: string;
  image?: string;
  summary: string;
}

export interface AIResponse {
  response: string;
  suggestedActions: SuggestedAction[];
  analysisData?: any;
  entityList?: EntityListItem[];
  showEntityDetails?: {
    entityId: string;
    entityType: string;
    data: any;
  };
  responseType: 'text' | 'entity_list' | 'entity_details' | 'update_form';
}

export class AIAssistantService {
  private static instance: AIAssistantService;
  
  static getInstance(): AIAssistantService {
    if (!AIAssistantService.instance) {
      AIAssistantService.instance = new AIAssistantService();
    }
    return AIAssistantService.instance;
  }

  async analyzeMessage(
    message: string,
    images: string[] = [],
    context: {
      farmId: string;
      currentEntities?: any[];
      userLocation?: { lat: number; lng: number };
    },
    language: string = 'en'
  ): Promise<AIResponse> {
    try {
      const systemPrompt = this.buildSystemPrompt(context, language);
      const userContent = await this.buildUserContent(message, images);

      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${OPENAI_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-4o',
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userContent }
          ],
          response_format: { type: 'json_object' },
          max_tokens: 1500,
          temperature: 0.7,
        }),
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error?.message || 'AI analysis failed');
      }

      const result = JSON.parse(data.choices[0].message.content);
      console.log('AI analysis result:', result?.response, result?.suggestedActions[0]?.data)

      // Determine response type based on the result
      let responseType: 'text' | 'entity_list' | 'entity_details' | 'update_form' = 'text';
      if (result.entityList && result.entityList.length > 0) {
        responseType = 'entity_list';
      } else if (result.showEntityDetails) {
        responseType = 'entity_details';
      } else if (result.suggestedActions && result.suggestedActions.length > 0) {
        responseType = 'update_form';
      }

      return {
        response: result.response || 'I analyzed your request.',
        suggestedActions: result.suggestedActions || [],
        analysisData: result.analysisData,
        entityList: result.entityList,
        showEntityDetails: result.showEntityDetails,
        responseType,
      };
    } catch (error) {
      console.error('AI analysis error:', error);
      throw error;
    }
  }

  // Add method to handle QR code scanning
  async handleQRCodeScan(qrData: string, context: any): Promise<AIResponse> {
    try {
      // Try to parse as universal link first
      const parsedLink = parseUniversalLink(qrData);
      
      if (parsedLink) {
        const { itemType, itemId, farmId } = parsedLink;
        
        // Check if this is for the current farm
        if (farmId !== context.farmId) {
          return {
            response: `This QR code is for a different farm. Please scan a QR code from your current farm.`,
            analysisData: {
              imageAnalysis: "QR code scanned but belongs to different farm",
              recommendations: ["Scan QR codes only from your current farm"],
              concerns: ["Farm ID mismatch"],
              confidence: 1.0
            },
            suggestedActions: [],
            responseType: 'text'
          };
        }

        // Find the entity in current farm data
        const entity = this.findEntityById(itemId, itemType, context);
        
        if (entity) {
          return {
            response: `Found ${itemType}: ${entity.name}. What would you like to do with this ${itemType}?`,
            analysisData: {
              imageAnalysis: `QR code scanned for existing ${itemType}: ${entity.name}`,
              recommendations: [
                `Update ${itemType} information`,
                `View ${itemType} details`,
                `Add photos or notes`,
                `Create maintenance task`
              ],
              concerns: [],
              confidence: 1.0
            },
            suggestedActions: [
              {
                id: `update_${itemType}_${Date.now()}`,
                type: "update",
                entity: itemType as any,
                title: `Update ${entity.name}`,
                description: `Modify information for this ${itemType}`,
                confidence: 0.95,
                data: { ...entity }
              }
            ],
            showEntityDetails: {
              entityId: itemId,
              entityType: itemType,
              data: entity
            },
            responseType: 'entity_details'
          };
        } else {
          return {
            response: `QR code is valid but the ${itemType} was not found in your farm. It may have been deleted or moved.`,
            analysisData: {
              imageAnalysis: "Valid QR code but entity not found",
              recommendations: ["Check if entity still exists", "Verify farm selection"],
              concerns: ["Entity not found in current farm"],
              confidence: 1.0
            },
            suggestedActions: [],
            responseType: 'text'
          };
        }
      }
      
      // If not a universal link, treat as regular message
      return await this.analyzeMessage("", [], context);

    } catch (error) {
      console.error('Error handling QR code:', error);
      return {
        response: "Unable to process this QR code. Please try scanning again or contact support.",
        analysisData: {
          imageAnalysis: "QR code processing failed",
          recommendations: ["Try scanning again", "Check QR code quality"],
          concerns: ["QR code processing error"],
          confidence: 0.0
        },
        suggestedActions: [],
        responseType: 'text'
      };
    }
  }

  private findEntityById(id: string, type: string, context: any): any {
    const entities = context.currentEntities || [];
    return entities.find((entity: any) => 
      (entity.id === id || entity.identificationID === id) && 
      entity.entityType === type
    );
  }

  private buildSystemPrompt(context: any, language: string = 'en'): string {
    const languageInstruction = language === 'ur'
      ? 'Always respond in Urdu language. Use proper Urdu script and agricultural terminology.'
      : 'Always respond in English language.';

    // Format current entities for the prompt
    const entitySummary = this.formatEntitiesForPrompt(context.currentEntities || []);
    const entityCount = context.currentEntities?.length || 0;

    return `You are an expert agricultural AI assistant for a farm management system with advanced image analysis capabilities.

Your role:
- Analyze user messages (text, images, or both)
- Extract detailed information from images using computer vision
- Provide helpful agricultural advice
- Show entity listings when users ask to see their farm data
- Display entity details when users want to view specific items
- Suggest specific actions to update farm data with complete entity details
- Always respond in JSON format
- ${languageInstruction}

CURRENT FARM ENTITIES (Total: ${entityCount}):
${entitySummary}

ENTITY LISTING CAPABILITIES:
When users ask to see their animals, plants, fields, gardens, or equipment, provide an entityList array with clickable items.
When users want to see details of a specific entity, use showEntityDetails object.
When users want to update something, provide suggestedActions array.

COMMON USER REQUESTS AND RESPONSES:
- "Show my animals" → Provide entityList with all animals
- "Show my plants" → Provide entityList with all plants
- "Show my fields" → Provide entityList with all fields
- "Show my gardens" → Provide entityList with all gardens
- "Show my equipment" → Provide entityList with all equipment
- "Add new [entity]" → Provide suggestedActions for creating new entity
- "View details of [entity name]" → Use showEntityDetails for that specific entity
- "Update [entity name]" → Provide suggestedActions for updating that entity

For entity listings, create EntityListItem objects with:
- id: entity ID
- name: entity name or identifier
- type: entity type (animal/plant/field/garden/equipment)
- status: current status if available
- image: image URL if available
- summary: brief description (e.g., "Holstein cow, 3 years old, healthy" or "Tomato plant, flowering stage")

IMPORTANT: Always maintain existing functionality:
- Continue to provide suggestedActions for create/update operations
- Use the existing ActionConfirmationModal workflow for data modifications
- Entity listings are for viewing/browsing, suggestedActions are for modifications
- When users want to add/create something, always provide suggestedActions
- When users want to update something, provide both showEntityDetails AND suggestedActions

IMPORTANT: For entity data, use EXACT text values from the lookup options below. The system will automatically convert these to proper lookup IDs:

PLANT LOOKUPS:
- Plant Growth Stages (status): "seedling", "growing", "flowering", "fruiting", "harvested", "dormant"
- Plant Health Status (health): "excellent", "good", "fair", "poor"

ANIMAL LOOKUPS:
- Animal species: "cattle", "goat", "sheep", "chicken"
- Animal gender: "male", "female"
- Animal health: "excellent", "good", "fair", "poor"

Use these EXACT values to ensure proper lookup ID conversion.

Farm Context:
- Farm ID: ${context.farmId}
- Available entities: animals, plants, gardens, fields, equipment
- Current entities count: ${context.currentEntities?.length || 0}

Entity Requirements:
ANIMAL: species (required), breed, gender, status, purpose, name, weight, weightUnit, dateOfBirth, identificationNumber, fieldId, notes
PLANT: name (required), species (required), variety, plantedDate (required), status, health, fieldId, notes, expectedHarvestDate
GARDEN: name (required), type (required), size, sizeUnit, status, soilType, irrigationSystem, notes
FIELD: name (required), type (required), size, sizeUnit, status, cropType, plantedDate, harvestDate, soilType, notes
EQUIPMENT: name (required), type (required), manufacturer, model, status, purchaseDate, purchasePrice, lastMaintenanceDate, notes

IDENTIFICATION ID GENERATION:
- Always include identificationID in suggested data for new entities
- Use format: ANM-XXXX for animals, PLT-XXXX for plants, GRD-XXXX for gardens, FLD-XXXX for fields, EQP-XXXX for equipment
- Generate unique 4-digit numbers (1000-9999 range)
- Example: "identificationID": "PLT-${Math.floor(1000 + Math.random() * 9000)}"

IMAGE ANALYSIS GUIDELINES:

For ANIMAL images, identify and extract:
- Species (cattle, sheep, goat, pig, chicken, etc.)
- Breed (Holstein, Angus, Merino, Yorkshire, Rhode Island Red, etc.)
- Gender (male/female based on physical characteristics)
- Age estimate (calf/juvenile/adult/senior based on size and features)
- Health status (healthy/sick/injured based on posture, eyes, coat condition)
- Body condition (thin/ideal/overweight based on visible body shape)
- Physical markings or distinctive features
- Estimated weight range based on size and breed

For PLANT images, identify and extract:
- Species (tomato, wheat, corn, apple, etc.)
- Variety (Roma tomato, Cherry tomato, Winter wheat, etc.)
- Growth stage (seedling/vegetative/flowering/fruiting/mature)
- Health status (healthy/stressed/diseased/pest damage)
- Specific diseases (blight, rust, powdery mildew, etc.)
- Nutrient deficiencies (nitrogen, phosphorus, potassium signs)
- Pest damage indicators
- Estimated planting date based on growth stage
- Expected harvest timeframe

For GARDEN images, identify and extract:
- Garden type (vegetable/herb/flower/mixed/greenhouse)
- Layout style (raised beds/rows/container/vertical)
- Plant varieties visible
- Soil condition (dry/moist/waterlogged/well-drained)
- Irrigation system type (drip/sprinkler/manual/none visible)
- Overall maintenance level
- Size estimation from visual cues
- Location indicators (indoor/outdoor/greenhouse)

For FIELD images, identify and extract:
- Crop type (wheat/corn/soybean/pasture/etc.)
- Field size estimation from perspective
- Crop growth stage and coverage density
- Soil condition (tilled/planted/harvested/fallow)
- Irrigation evidence (pivot systems/channels/dry farming)
- Terrain type (flat/sloped/terraced)
- Weed presence and severity
- Disease or pest damage signs
- Harvest readiness indicators

For EQUIPMENT images, identify and extract:
- Equipment type (tractor/harvester/plow/sprayer/cultivator/etc.)
- Manufacturer/brand (John Deere/Case IH/New Holland/etc.)
- Model identification from visible markings
- Size category (compact/utility/large/industrial)
- Condition assessment (excellent/good/fair/poor/needs repair)
- Visible wear, damage, or missing parts
- Age estimation from design and condition
- Attachments or implements visible
- Maintenance indicators (rust/paint condition/tire wear)

Response Format Examples:

FOR ENTITY LISTING (when user asks "show my animals"):
{
  "response": "Here are your animals:",
  "entityList": [
    {
      "id": "animal_id_123",
      "name": "Bella",
      "type": "animal",
      "status": "healthy",
      "image": "image_url_if_available",
      "summary": "Holstein cow, 3 years old, healthy"
    }
  ]
}

FOR CREATE/UPDATE ACTIONS (when user wants to add/modify):
{
  "response": "I can help you add a new animal. Please review the details:",
  "suggestedActions": [
    {
      "id": "create_animal_${Date.now()}",
      "type": "create",
      "entity": "animal",
      "title": "Add Holstein Cow",
      "description": "Create new Holstein cow based on image analysis",
      "confidence": 0.85,
      "data": {
        "identificationID": "ANM-${Math.floor(1000 + Math.random() * 9000)}",
        "name": "Holstein Cow #1",
        "species": "Cow",
        "breed": "Holstein",
        // ... complete entity data
      }
    }
  ]
}

FOR ENTITY DETAILS (when user clicks on entity or asks for specific details):
{
  "response": "Here are the details for Bella:",
  "showEntityDetails": {
    "entityId": "animal_id_123",
    "entityType": "animal",
    "data": {
      // Complete entity data object
    }
  },
  "suggestedActions": [
    {
      "id": "update_animal_123",
      "type": "update",
      "entity": "animal",
      "title": "Update Bella",
      "description": "Update information for this cow",
      "confidence": 0.9,
      "data": {
        // Current entity data for editing
      }
    }
  ]
}
  "entityList": [
    {
      "id": "entity_id",
      "name": "Entity Name",
      "type": "animal|plant|garden|field|equipment",
      "status": "current status",
      "image": "image_url_if_available",
      "summary": "Brief description for display"
    }
  ],
  "showEntityDetails": {
    "entityId": "entity_id",
    "entityType": "animal|plant|garden|field|equipment",
    "data": {
      // Complete entity data for detailed view
    }
  }
}

SPECIFIC EXTRACTION RULES:
1. For animals: Always try to identify breed-specific characteristics, estimate age from size/features, assess health from posture/eyes/coat
2. For plants: Look for leaf shape, flower/fruit characteristics, growth patterns to identify species and variety
3. For equipment: Check for brand logos, model numbers, design features that indicate manufacturer and type
4. For fields/gardens: Assess crop density, growth uniformity, soil visibility, irrigation infrastructure
5. Use your agricultural knowledge to provide realistic estimates for weights, sizes, dates, and conditions
6. When uncertain, indicate confidence level and provide best estimates with appropriate caveats
7. Always include specific, actionable recommendations based on what you observe

Guidelines:
- ALWAYS include identificationID field for create actions
- Generate realistic identification IDs using the specified format
- Include ALL mandatory fields for each entity type
- Use proper data types (dates as ISO strings, numbers as numbers)
- Generate meaningful names that include species/breed/variety information`;
  }

  private async buildUserContent(message: string, images: string[]): Promise<any[]> {
    const content: any[] = [
      { type: 'text', text: message }
    ];

    for (const imageUri of images) {
      try {
        const base64Image = await FileSystem.readAsStringAsync(imageUri, {
          encoding: FileSystem.EncodingType.Base64,
        });
        
        content.push({
          type: 'image_url',
          image_url: { url: `data:image/jpeg;base64,${base64Image}` }
        });
      } catch (error) {
        console.error('Error processing image:', error);
      }
    }

    return content;
  }

  private formatEntitiesForPrompt(entities: any[]): string {
    if (!entities || entities.length === 0) {
      return "No entities currently in the farm.";
    }

    const entityGroups = {
      animals: entities.filter(e => e.species && !e.type), // Animals have species but no type
      plants: entities.filter(e => e.species && e.plantedDate), // Plants have species and plantedDate
      fields: entities.filter(e => e.type && !e.species && !e.gardenType), // Fields have type but no species
      gardens: entities.filter(e => e.gardenType || (e.type && e.soilType)), // Gardens have gardenType or type+soilType
      equipment: entities.filter(e => e.manufacturer || e.model || (e.type && !e.soilType)) // Equipment has manufacturer/model
    };

    let summary = "";

    if (entityGroups.animals.length > 0) {
      summary += `\nANIMALS (${entityGroups.animals.length}):\n`;
      entityGroups.animals.slice(0, 5).forEach(animal => {
        summary += `- ${animal.name || animal.species} (ID: ${animal.id}, Status: ${animal.status || 'N/A'})\n`;
      });
      if (entityGroups.animals.length > 5) summary += `... and ${entityGroups.animals.length - 5} more\n`;
    }

    if (entityGroups.plants.length > 0) {
      summary += `\nPLANTS (${entityGroups.plants.length}):\n`;
      entityGroups.plants.slice(0, 5).forEach(plant => {
        summary += `- ${plant.name} (ID: ${plant.id}, Species: ${plant.species}, Status: ${plant.status || 'N/A'})\n`;
      });
      if (entityGroups.plants.length > 5) summary += `... and ${entityGroups.plants.length - 5} more\n`;
    }

    if (entityGroups.fields.length > 0) {
      summary += `\nFIELDS (${entityGroups.fields.length}):\n`;
      entityGroups.fields.slice(0, 5).forEach(field => {
        summary += `- ${field.name} (ID: ${field.id}, Type: ${field.type}, Status: ${field.status || 'N/A'})\n`;
      });
      if (entityGroups.fields.length > 5) summary += `... and ${entityGroups.fields.length - 5} more\n`;
    }

    if (entityGroups.gardens.length > 0) {
      summary += `\nGARDENS (${entityGroups.gardens.length}):\n`;
      entityGroups.gardens.slice(0, 5).forEach(garden => {
        summary += `- ${garden.name} (ID: ${garden.id}, Type: ${garden.type || garden.gardenType}, Status: ${garden.status || 'N/A'})\n`;
      });
      if (entityGroups.gardens.length > 5) summary += `... and ${entityGroups.gardens.length - 5} more\n`;
    }

    if (entityGroups.equipment.length > 0) {
      summary += `\nEQUIPMENT (${entityGroups.equipment.length}):\n`;
      entityGroups.equipment.slice(0, 5).forEach(equipment => {
        summary += `- ${equipment.name} (ID: ${equipment.id}, Type: ${equipment.type}, Status: ${equipment.status || 'N/A'})\n`;
      });
      if (entityGroups.equipment.length > 5) summary += `... and ${entityGroups.equipment.length - 5} more\n`;
    }

    return summary || "No entities found in the farm.";
  }
}


