import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Alert, StyleSheet, ScrollView } from 'react-native';
import { convertAIDataToLookupIds, validateEntityData } from '@/utils/lookup-validation';
import { colors } from '@/constants/colors';

/**
 * Test component to demonstrate AI data conversion to lookup IDs
 */
export default function AILookupValidationTest() {
  const [testResults, setTestResults] = useState<any>(null);

  // Simulate AI-generated data with text values (like what AI assistant would create)
  const simulateAIPlantData = {
    name: 'Tomato Plant #1',
    species: 'Tomato',
    status: 'growing',      // Text value that needs to be converted to lookup ID
    health: 'good',         // Text value that needs to be converted to lookup ID
    variety: 'Roma',
    farmId: 'test-farm-id',
    plantedDate: new Date().toISOString(),
  };

  const simulateAIAnimalData = {
    name: 'Holstein Cow #1',
    species: 'cattle',      // Text value that needs to be converted to lookup ID
    breed: 'holstein',      // Text value that needs to be converted to lookup ID
    gender: 'female',       // Text value that needs to be converted to lookup ID
    status: 'healthy',      // Text value that needs to be converted to lookup ID
    purpose: 'dairy',       // Text value that needs to be converted to lookup ID
    farmId: 'test-farm-id',
  };

  const testPlantConversion = () => {
    console.log('Testing Plant Data Conversion...');
    console.log('Original AI Data:', simulateAIPlantData);

    // Step 1: Convert AI text values to lookup IDs
    const { convertedData, conversionLog } = convertAIDataToLookupIds('plant', simulateAIPlantData);
    console.log('Converted Data:', convertedData);
    console.log('Conversion Log:', conversionLog);

    // Step 2: Validate the converted data
    const { sanitizedData, validationErrors, isValid } = validateEntityData('plant', convertedData);
    console.log('Sanitized Data:', sanitizedData);
    console.log('Validation Errors:', validationErrors);
    console.log('Is Valid:', isValid);

    setTestResults({
      type: 'Plant',
      original: simulateAIPlantData,
      converted: convertedData,
      conversionLog,
      sanitized: sanitizedData,
      validationErrors,
      isValid
    });

    Alert.alert(
      'Plant Conversion Test',
      `Conversion: ${conversionLog.length} fields processed\nValidation: ${isValid ? 'PASSED' : 'FAILED'}\n\nCheck console for details.`
    );
  };

  const testAnimalConversion = () => {
    console.log('Testing Animal Data Conversion...');
    console.log('Original AI Data:', simulateAIAnimalData);

    // Step 1: Convert AI text values to lookup IDs
    const { convertedData, conversionLog } = convertAIDataToLookupIds('animal', simulateAIAnimalData);
    console.log('Converted Data:', convertedData);
    console.log('Conversion Log:', conversionLog);

    // Step 2: Validate the converted data
    const { sanitizedData, validationErrors, isValid } = validateEntityData('animal', convertedData);
    console.log('Sanitized Data:', sanitizedData);
    console.log('Validation Errors:', validationErrors);
    console.log('Is Valid:', isValid);

    setTestResults({
      type: 'Animal',
      original: simulateAIAnimalData,
      converted: convertedData,
      conversionLog,
      sanitized: sanitizedData,
      validationErrors,
      isValid
    });

    Alert.alert(
      'Animal Conversion Test',
      `Conversion: ${conversionLog.length} fields processed\nValidation: ${isValid ? 'PASSED' : 'FAILED'}\n\nCheck console for details.`
    );
  };

  const testInvalidData = () => {
    const invalidData = {
      name: 'Test Plant',
      species: 'Unknown Species',
      status: 'invalid_status',
      health: 'super_healthy',
      farmId: 'test-farm-id',
    };

    console.log('Testing Invalid Data Handling...');
    console.log('Invalid AI Data:', invalidData);

    const { convertedData, conversionLog } = convertAIDataToLookupIds('plant', invalidData);
    const { sanitizedData, validationErrors, isValid } = validateEntityData('plant', convertedData);

    setTestResults({
      type: 'Invalid Data Test',
      original: invalidData,
      converted: convertedData,
      conversionLog,
      sanitized: sanitizedData,
      validationErrors,
      isValid
    });

    Alert.alert(
      'Invalid Data Test',
      `This test shows how invalid data is handled.\nValidation: ${isValid ? 'PASSED' : 'FAILED'}\nErrors: ${validationErrors.length}`
    );
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>AI Lookup Validation Test</Text>
      <Text style={styles.description}>
        This component tests the conversion of AI-generated text values to proper lookup IDs.
        The AI assistant creates entities with text values like "growing", "healthy", "cattle", etc.
        These need to be converted to the actual lookup IDs before saving to Firestore.
      </Text>

      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.button} onPress={testPlantConversion}>
          <Text style={styles.buttonText}>Test Plant Data Conversion</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={testAnimalConversion}>
          <Text style={styles.buttonText}>Test Animal Data Conversion</Text>
        </TouchableOpacity>

        <TouchableOpacity style={[styles.button, styles.warningButton]} onPress={testInvalidData}>
          <Text style={styles.buttonText}>Test Invalid Data Handling</Text>
        </TouchableOpacity>
      </View>

      {testResults && (
        <View style={styles.resultsContainer}>
          <Text style={styles.resultsTitle}>Test Results ({testResults.type}):</Text>
          
          <Text style={styles.sectionTitle}>Original AI Data:</Text>
          <Text style={styles.codeText}>{JSON.stringify(testResults.original, null, 2)}</Text>
          
          <Text style={styles.sectionTitle}>Conversion Log:</Text>
          {testResults.conversionLog.map((log: string, index: number) => (
            <Text key={index} style={styles.logText}>• {log}</Text>
          ))}
          
          <Text style={styles.sectionTitle}>Final Sanitized Data (Ready for Firestore):</Text>
          <Text style={styles.codeText}>{JSON.stringify(testResults.sanitized, null, 2)}</Text>
          
          <View style={[styles.statusContainer, { backgroundColor: testResults.isValid ? colors.success : colors.danger }]}>
            <Text style={styles.statusText}>
              {testResults.isValid ? '✅ VALIDATION PASSED' : '❌ VALIDATION FAILED'}
            </Text>
          </View>
          
          {testResults.validationErrors.length > 0 && (
            <>
              <Text style={styles.sectionTitle}>Validation Errors:</Text>
              {testResults.validationErrors.map((error: string, index: number) => (
                <Text key={index} style={styles.errorText}>• {error}</Text>
              ))}
            </>
          )}
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: colors.white,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    color: colors.gray[800],
  },
  description: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 20,
    lineHeight: 20,
  },
  buttonContainer: {
    marginBottom: 20,
  },
  button: {
    backgroundColor: colors.primary,
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  warningButton: {
    backgroundColor: colors.warning,
  },
  buttonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  resultsContainer: {
    marginTop: 20,
    padding: 15,
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  resultsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: colors.gray[800],
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 15,
    marginBottom: 5,
    color: colors.gray[700],
  },
  codeText: {
    fontSize: 11,
    color: colors.gray[800],
    backgroundColor: colors.gray[100],
    padding: 10,
    borderRadius: 4,
    fontFamily: 'monospace',
  },
  logText: {
    fontSize: 12,
    color: colors.gray[600],
    marginBottom: 2,
  },
  statusContainer: {
    padding: 10,
    borderRadius: 6,
    marginTop: 15,
    alignItems: 'center',
  },
  statusText: {
    color: colors.white,
    fontSize: 14,
    fontWeight: 'bold',
  },
  errorText: {
    fontSize: 12,
    color: colors.danger,
    marginBottom: 2,
  },
});
