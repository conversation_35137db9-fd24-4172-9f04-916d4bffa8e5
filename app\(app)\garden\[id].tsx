import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Alert as RNAlert,
  ActivityIndicator,
  Modal,
} from 'react-native';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import Button from '@/components/Button';
import {
  TreeDeciduous,
  Ruler,
  Droplets,
  Edit,
  Trash2,
  ChevronDown,
  Check,
  X,
  Camera,
  Image as ImageIcon,
  MapPin,
  QrCode,
  Archive,
  Calendar,
  Sprout,
  DollarSign,
  EarthIcon
} from 'lucide-react-native';
import ImagePicker from '@/components/ImagePicker';
import { uploadImageAsync } from '@/utils/firebase-storage';
import { TextInput } from 'react-native';
import QRCodeDisplay from '@/components/QRCodeDisplay';
import { generateUniversalLink } from '@/utils/qrcode';
import InactiveStatusModal from '@/components/InactiveStatusModal';
import Toast from 'react-native-toast-message';
import FinanceHandlerButton from '@/components/FinanceHandlerButton';
import FinanceBottomSheet from '@/components/FinanceBottomSheet';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { useTranslation } from '@/i18n/useTranslation';
import { EntityGallery } from '@/components/EntityGallery';
import { useLookupStore } from '@/store/lookup-store';
import { ImageSlider } from '@/components/ImageSlider';
import { useEntityGallery } from '@/hooks/useEntityGallery';


export default function GardenDetailsScreen() {
  const { id } = useLocalSearchParams();

  // console.log({id})
  const {
    gardens,
    getGarden,
    deleteGarden,
    updateGarden,
    plants,
    currentFarm,
    markGardenInactive,
    loadEntityGallery
  } = useFarmStore();
  const { language } = useAuthStore();
  const { t, isRTL } = useTranslation();
  const { lookupsList, fetchLookups } = useLookupStore();
  const [garden, setGarden] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showStatusDropdown, setShowStatusDropdown] = useState(false);
  const [showImageModal, setShowImageModal] = useState(false);
  const [selectedImage, setSelectedImage] = useState('');
  const [newImageUri, setNewImageUri] = useState('');
  const [isAddingImage, setIsAddingImage] = useState(false);
  const [showQRModal, setShowQRModal] = useState(false);
  const [showInactiveModal, setShowInactiveModal] = useState(false);
  const [activeTab, setActiveTab] = useState("details")
  // Editable fields
  const [status, setStatus] = useState<'active' | 'inactive' | 'planning'>('active');
  const [irrigationSystem, setIrrigationSystem] = useState('');
  const [notes, setNotes] = useState('');

  // Get plants in this garden
  const [gardenPlants, setGardenPlants] = useState<any[]>([]);

  // Add this ref for the bottom sheet
  const financeBottomSheetRef = useRef<BottomSheetModal>(null);

  // Add this state for controlling visibility
  const [showFinanceBottomSheet, setShowFinanceBottomSheet] = useState(false);

  // Add this function to handle opening the finance bottom sheet
  function handleOpenFinanceBottomSheet(): void {
    financeBottomSheetRef.current?.present();
    setShowFinanceBottomSheet(true);
  }

  // Add this function to handle closing the finance bottom sheet
  function handleCloseFinanceBottomSheet(): void {
    financeBottomSheetRef.current?.dismiss();
    setShowFinanceBottomSheet(false);
  }

  // Ensure lookups are loaded
  useEffect(() => {
    fetchLookups();
  }, [fetchLookups]);

  useEffect(() => {
    if (id) {
      const gardenData = getGarden(id as string);
      if (gardenData) {
        setGarden(gardenData);
        setStatus(gardenData.status || 'active');
        setIrrigationSystem(gardenData.irrigationSystem || '');
        setNotes(gardenData.notes || '');
      }
      setLoading(false);
    }
  }, [id, gardens, lookupsList]); // Add lookupsList as dependency

  useEffect(() => {
    if (garden) {
      // Filter plants that belong to this garden
      // console.log({ plants }, plants?.length)
      const filteredPlants = plants.filter(plant => plant.gardenId === garden.id);
      setGardenPlants(filteredPlants);
    }
  }, [garden, plants]);

  // Load gallery photos
  useEffect(() => {
    const loadGalleryPhotos = async () => {
      if (garden?.id && currentFarm?.id && !garden?.photos) {
        const galleryPhotos = await loadEntityGallery(currentFarm.id, garden.id, 'garden');
        setGarden(prev => ({
          ...prev,
          photos: galleryPhotos
        }));
      }
    };

    loadGalleryPhotos();
  }, [garden?.id, currentFarm?.id]); // Remove garden from dependencies to prevent infinite loop

  const isInactive = garden?.isInactive === true;

  if (loading) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
      </SafeAreaView>
    );
  }

  if (!garden) {
    return (
      <SafeAreaView style={styles.errorContainer}>
        <Text style={styles.errorText}>Garden not found</Text>
        <Button
          title="Go Back"
          onPress={() => router.back()}
          style={styles.errorButton}
        />
      </SafeAreaView>
    );
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getSizeUnitText = (unit: string) => {
    switch (unit) {
      case 'sq_m': return 'm²';
      case 'sq_ft': return 'ft²';
      case 'acres': return 'acres';
      case 'ha': return 'ha';
      default: return unit;
    }
  };

  const getStatusColor = (statusValue: string) => {
    switch (statusValue) {
      case 'active':
        return colors.success;
      case 'inactive':
        return colors.gray[500];
      case 'planning':
        return colors.info;
      default:
        return colors.gray[500];
    }
  };

  const handleDelete = () => {
    RNAlert.alert(
      'Delete Garden',
      'Are you sure you want to delete this garden? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteGarden(garden.id);
              Toast.show({
                type: 'success',
                text1: 'Success',
                text2: 'Garden deleted successfully',
              });
              router.back();
            } catch (error) {
              console.error('Error deleting garden:', error);
              Toast.show({
                type: 'error',
                text1: 'Error',
                text2: 'Failed to delete garden',
              });
            }
          }
        }
      ]
    );
  };

  const handleSaveChanges = async () => {
    try {
      const updatedGarden = {
        ...garden,
        status,
        irrigationSystem,
        notes,
        updatedAt: new Date()
      };

      await updateGarden(garden.id, updatedGarden);
      setIsEditing(false);
      RNAlert.alert('Success', 'Garden updated successfully');
    } catch (error) {
      console.error('Error updating garden:', error);
      RNAlert.alert('Error', 'Failed to update garden');
    }
  };

  const handleAddImage = async () => {
    if (!newImageUri) {
      RNAlert.alert('Error', 'Please select an image first');
      return;
    }

    try {
      setIsAddingImage(true);

      // Upload the new image
      const imageUrl = await uploadImageAsync(newImageUri, 'gardens');

      // Add the new photo to the garden's photos array
      const updatedPhotos = [
        ...(garden.photos || []),
        {
          url: imageUrl,
          timestamp: new Date(),
          takenBy: ''
        }
      ];

      // Update the garden with the new photos array
      const updatedGarden = {
        ...garden,
        photos: updatedPhotos,
        updatedAt: new Date()
      };

      await updateGarden(garden.id, updatedGarden);
      setGarden(updatedGarden);
      setNewImageUri('');
      setIsAddingImage(false);
      RNAlert.alert('Success', 'Image added successfully');
    } catch (error) {
      console.error('Error adding image:', error);
      RNAlert.alert('Error', 'Failed to add image');
      setIsAddingImage(false);
    }
  };

  // Generate QR code value
  const getQRCodeValue = () => {
    if (!garden || !currentFarm) return '';
    return generateUniversalLink('garden', garden.id, currentFarm.id);
  };

  const handleMarkInactive = async (data: {
    reason: string;
    notes?: string;
    image?: string;
    cascadeToPlants?: boolean;
  }) => {
    try {
      await markGardenInactive(garden.id, data);
      Toast.show({
        type: 'success',
        text1: 'Success',
        text2: 'Garden marked as inactive',
      });
    } catch (error) {
      console.error('Error marking garden as inactive:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to mark garden as inactive',
      });
    }
  };

  const renderOverviewTab = () => {
    return (
      <>
        <View style={styles.infoSection}>
          <View style={[styles.infoRow, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
            <View style={[styles.infoItem, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
              <Ruler size={20} color={colors.gray[600]} style={styles.infoIcon} />
              <View>
                <Text style={[styles.infoLabel, { textAlign: isRTL ? "right" : "left", marginRight: isRTL ? 8 : 0 }]}>{t('entity.garden.size')}</Text>
                <Text style={[styles.infoValue, { textAlign: isRTL ? "right" : "left", marginRight: isRTL ? 8 : 0 }]}>
                  {garden.size?.toFixed(2)} {t(`common.areaUnit.${garden.sizeUnit}`)}
                </Text>
              </View>
            </View>

            <View style={[styles.infoItem, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
              <MapPin size={20} color={colors.gray[600]} style={styles.infoIcon} />
              <View>
                <Text style={[styles.infoLabel, { textAlign: isRTL ? "right" : "left", marginRight: isRTL ? 8 : 0 }]}>{t('entity.garden.location')}</Text>
                <Text style={[styles.infoValue, { textAlign: isRTL ? "right" : "left", marginRight: isRTL ? 8 : 0 }]}>
                  {garden.location ? garden.location.address || 'Coordinates available' : 'Not specified'}
                </Text>
              </View>
            </View>
          </View>

          <View style={[styles.infoRow, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
            {/* {isEditing ? (
              <View style={styles.irrigationContainer}>
                <Text style={styles.irrigationLabel}>Irrigation System</Text>
                <TextInput
                  style={styles.irrigationInput}
                  value={irrigationSystem}
                  onChangeText={setIrrigationSystem}
                  placeholder="Enter irrigation system details"
                />
              </View>
            ) : (
             
            )} */}
            <View style={[styles.infoItem, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
              <Droplets size={20} color={colors.gray[600]} style={styles.infoIcon} />
              <View>
                <Text style={[styles.infoLabel, { textAlign: isRTL ? "right" : "left", marginRight: isRTL ? 8 : 0 }]}>{t("entity.garden.irrigationSystem")}</Text>
                <Text style={[styles.infoValue, { textAlign: isRTL ? "right" : "left", marginRight: isRTL ? 8 : 0 }]}>
                  {t(`entity.garden.irrigationSystem${garden.irrigationSystem}`) || 'Not specified'}
                </Text>
              </View>
            </View>
          </View>

          {garden.soilType && (
            <View style={[styles.soilTypeContainer, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
              <EarthIcon size={20} color={colors.gray[600]} style={styles.infoIcon} />
              <View>
                <Text style={[styles.soilTypeLabel, { textAlign: isRTL ? "right" : "left", marginRight: isRTL ? 8 : 0 }]}>{t('entity.garden.soilType')}</Text>
                <View style={[styles.soilTypeBadge, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                  <Text style={[styles.soilTypeText, { textAlign: isRTL ? "right" : "left", marginRight: isRTL ? 8 : 0 }]}>{garden.soilType}</Text>
                </View>
              </View>
            </View>
          )}
        </View>
        <View style={[styles.quickActionsContainer]}>
          <Text style={[styles.sectionTitle, { textAlign: isRTL ? "right" : "left", marginRight: isRTL ? 8 : 0 }]}>{t('common.quickActions')}</Text>
          <View style={[styles.quickActions, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
            <TouchableOpacity
              style={[
                styles.quickActionButton,
                isInactive && styles.disabledActionButton
                , { flexDirection: isRTL ? 'row-reverse' : 'row' }
              ]}
              onPress={() => router.push(`/task/create?id=${garden.id}&entity=garden`)}

              // onPress={handleCreateTask}
              disabled={isInactive}
            >
              <View style={[
                styles.quickActionIcon,
                { backgroundColor: isInactive ? colors.gray[400] : colors.warning }
              ]}>
                <Calendar size={20} color={colors.white} />
              </View>
              <Text style={[
                styles.quickActionText,
                isInactive && styles.disabledActionText,
                { textAlign: isRTL ? "right" : "left", marginRight: isRTL ? 8 : 0 }
              ]}>{t('add.task')}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.quickActionButton,
                isInactive && styles.disabledActionButton
                , { flexDirection: isRTL ? 'row-reverse' : 'row' }
              ]}
              onPress={() => router.push(`/plant/create?gardenId=${garden.id}`)}
              disabled={isInactive}
            >
              <View style={[
                styles.quickActionIcon,
                { backgroundColor: isInactive ? colors.gray[400] : colors.success }
              ]}>
                <Sprout size={20} color={colors.white} />
              </View>
              <Text style={[
                styles.quickActionText,
                isInactive && styles.disabledActionText,
                { textAlign: isRTL ? "right" : "left", marginRight: isRTL ? 8 : 0 }
              ]}>{t('add.plant')}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.quickActionButton, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}
              onPress={() => router.push({
                pathname: '/garden/create?id=' + garden.id,
                // params: { editMode: true, id: garden.id }
              })}
            >
              <View style={[styles.quickActionIcon, { backgroundColor: colors.primary }]}>
                <Edit size={20} color={colors.white} />
              </View>
              <Text style={[styles.quickActionText, { textAlign: isRTL ? "right" : "left", marginRight: isRTL ? 8 : 0 }]}>{t('entity.garden.edit')}</Text>
            </TouchableOpacity>

            {!isInactive && (
              <TouchableOpacity
                style={[styles.quickActionButton, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}
                onPress={() => setShowInactiveModal(true)}
              >
                <View style={[styles.quickActionIcon, { backgroundColor: colors.gray[500] }]}>
                  <Archive size={20} color={colors.white} />
                </View>
                <Text style={[styles.quickActionText, { textAlign: isRTL ? "right" : "left", marginRight: isRTL ? 8 : 0 }]}>{t('common.markInactive')}</Text>
              </TouchableOpacity>
            )}
            <TouchableOpacity
              style={[
                styles.quickActionButton,
                isInactive && styles.disabledActionButton
                , { flexDirection: isRTL ? 'row-reverse' : 'row' }
              ]}
              // router.push(`/finance/create?id=${id}`)
              onPress={() => { router.push(`/finance/create?id=${id}&entity=garden`) }}
              // onPress={() => router.push(`/finance/create?entityId=${garden.id}&entityType=garden`)}
              disabled={isInactive}
            >
              <View style={[
                styles.quickActionIcon,
                { backgroundColor: isInactive ? colors.gray[400] : colors.gray[700] }
              ]}>
                <DollarSign size={20} color={colors.white} />
              </View>
              <Text style={[
                styles.quickActionText,
                isInactive && styles.disabledActionText,
                { textAlign: isRTL ? "right" : "left", marginRight: isRTL ? 8 : 0 }
              ]}>{t('finance.manageFinances')}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </>
    )
  }
  function renderQRCodeTab(): React.ReactNode {
    return (
      <View style={styles.tabContent}>
        <Text style={styles.sectionTitle}>{t('qrCode.QRCode')}</Text>
        <Text style={styles.qrCodeDescription}>
          {t('qrCode.quickAccess')}
        </Text>
        <View style={{ alignItems: 'center', marginVertical: 24 }}>
          <QRCodeDisplay
            value={getQRCodeValue()}
            size={200}
            itemType="garden"
            itemName={garden.name}
          />
        </View>
        <Text style={styles.qrModalInstructions}>
          {t('qrCode.instructions') || 'You can save this QR code to your device or share it with others.'}
        </Text>
      </View>
    );
  }
  const renderPlantsList = () => {
    return (
      <View style={styles.plantsSection}>
        <View style={[styles.sectionHeader, isRTL && { flexDirection: "row-reverse" }]}>
          <Text style={styles.sectionTitle}>{t('entity.plant.plural')}</Text>
          <TouchableOpacity
            style={styles.addPlantButton}
            onPress={() => router.push('/plant/create')}
          >
            <Text style={styles.addPlantText}>{t('add.plant')}</Text>
          </TouchableOpacity>
        </View>

        {gardenPlants.length > 0 ? (
          <View style={styles.plantsList}>
            {gardenPlants.map((plant) => (
              <TouchableOpacity
                key={plant.id}
                style={[styles.plantCard, isRTL && { flexDirection: "row-reverse" }]}
                onPress={() => router.push(`/plant/${plant.id}`)}
              >
                <Image
                  source={{ uri: plant.image || plant.photos?.[0]?.url || 'https://images.unsplash.com/photo-1591857177580-dc82b9ac4e1e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80' }}
                  style={styles.plantImage}
                  resizeMode="cover"
                />
                <View style={styles.plantInfo}>
                  <Text style={[styles.plantName, isRTL && { textAlign: 'right', marginRight: 8 }]}>{plant.name || plant.species}</Text>
                  <Text style={[styles.plantSpecies, isRTL && { textAlign: 'right', marginRight: 8 }]}>{plant.species}</Text>
                  <View style={[styles.plantStatusBadge, isRTL && { flexDirection: "row-reverse", marginRight: 8 }, { backgroundColor: getPlantStatusColor(plant.status) }]}>
                    <Text style={styles.plantStatusText}>
                      {plant.status.charAt(0).toUpperCase() + plant.status.slice(1)}
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        ) : (
          <View style={styles.noPlants}>
            <Text style={styles.noPlantsText}>{t('entity.garden.noPlants')}</Text>
          </View>
        )}
      </View>
    )
  }

  function renderGalleryTab(): React.ReactNode {
    return (
      <View style={styles.tabContent}>
        <EntityGallery
          entityId={garden.id}
          entityType="garden"
          isEditable={!isInactive}
          maxPhotos={20}
          showTimestamp={true}
          showDeleteOption={true}
          gridColumns={2}
          imageSize="large"
          emptyStateMessage={t('gallery.title.garden')}
          onPhotosChange={(photos) => {
            // Update local garden state if needed
            setGarden(prev => ({
              ...prev,
              photos: photos
            }));
          }}
        />
      </View>
    );
  }
  // </Text></View></Text>
  return (
    <>
      <Stack.Screen
        options={{
          title: garden?.name || 'Garden Details',
          headerRight: () => (
            <></>
          ),
        }}
      />

      <SafeAreaView style={styles.container}>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          {/* Display garden images with slider */}
          {garden.photos && garden.photos.length > 0 ? (
            <View style={styles.imageSliderContainer}>
              <ImageSlider
                images={garden.photos.map(photo => photo.url)}
                height={200}
                showIndicators={true}
              />
            </View>
          ) : garden.image ? (
            <View style={styles.imageContainer}>
              <Image
                source={{ uri: garden.image }}
                style={styles.coverImage}
                resizeMode="cover"
              />
            </View>
          ) : null}

          {isInactive && (
            <View style={styles.inactiveBanner}>
              <Archive size={16} color={colors.white} />
              <Text style={styles.inactiveBannerText}>
                {t('entity.garden.thisgardenIsInactive')}
                {garden.inactiveReason ? `: ${garden.inactiveReason}` : ''}
                {garden.inactiveDate ? ` (${new Date(garden.inactiveDate).toLocaleDateString()})` : ''}
              </Text>
            </View>
          )}

          <View style={styles.contentContainer}>
            <View style={[styles.header, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
              <View style={[styles.titleContainer, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                <TreeDeciduous size={24} color={colors.primary} style={styles.titleIcon} />
                <View>
                  <Text style={styles.title}>{garden.name}</Text>
                  <Text style={styles.subtitle}>
                    {garden.type.charAt(0).toUpperCase() + garden.type.slice(1)} Garden
                  </Text>
                </View>
              </View>

              <View style={styles.statusContainer}>
                {isEditing ? (
                  <TouchableOpacity
                    style={styles.statusDropdownContainer}
                    onPress={() => setShowStatusDropdown(!showStatusDropdown)}
                  >
                    <View style={[styles.statusBadge, { backgroundColor: getStatusColor(status) }]}>
                      <Text style={styles.statusText}>
                        {status.charAt(0).toUpperCase() + status.slice(1)}
                      </Text>
                      <ChevronDown size={16} color={colors.white} />
                    </View>

                    {showStatusDropdown && (
                      <View style={styles.dropdownMenu}>
                        {['active', 'inactive', 'planning'].map((item) => (
                          <TouchableOpacity
                            key={item}
                            style={styles.dropdownItem}
                            onPress={() => {
                              setStatus(item as any);
                              setShowStatusDropdown(false);
                            }}
                          >
                            <View style={[styles.statusIndicator, { backgroundColor: getStatusColor(item) }]} />
                            <Text style={styles.dropdownItemText}>
                              {item.charAt(0).toUpperCase() + item.slice(1)}
                            </Text>
                          </TouchableOpacity>
                        ))}
                      </View>
                    )}
                  </TouchableOpacity>
                ) : (
                  <View style={[styles.statusBadge, { backgroundColor: getStatusColor(garden.status) }]}>
                    <Text style={styles.statusText}>
                      {garden.status.charAt(0).toUpperCase() + garden.status.slice(1)}
                    </Text>
                  </View>
                )}
              </View>
            </View>
            <View style={styles.tabsContainer}>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={[styles.tabs, isRTL && { flexDirection: 'row-reverse' }]}
              >
                <TouchableOpacity
                  style={[styles.tab, activeTab === 'details' && styles.activeTab]}
                  onPress={() => setActiveTab('details')}
                >
                  <Text style={[styles.tabText, activeTab === 'details' && styles.activeTabText]}>{t('entity.garden.details')}</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.tab, activeTab === 'plants' && styles.activeTab]}
                  onPress={() => setActiveTab('plants')}
                >
                  <Text style={[styles.tabText, activeTab === 'plants' && styles.activeTabText]}>{t('entity.garden.plants')}</Text>
                </TouchableOpacity>
                {/* <TouchableOpacity
                  style={[styles.tab, activeTab === 'checklists' && styles.activeTab]}
                  onPress={() => setActiveTab('checklists')}
                >
                  <Text style={[styles.tabText, activeTab === 'checklists' && styles.activeTabText]}>Checklists</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.tab, activeTab === 'history' && styles.activeTab]}
                  onPress={() => setActiveTab('history')}
                >
                  <Text style={[styles.tabText, activeTab === 'history' && styles.activeTabText]}>History</Text>
                </TouchableOpacity> */}
                <TouchableOpacity
                  style={[styles.tab, activeTab === 'gallery' && styles.activeTab]}
                  onPress={() => setActiveTab('gallery')}
                >
                  <Text style={[styles.tabText, activeTab === 'gallery' && styles.activeTabText]}>{t('entity.garden.gallery')}</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.tab, activeTab === 'qrcode' && styles.activeTab]}
                  onPress={() => setActiveTab('qrcode')}
                >
                  <Text style={[styles.tabText, activeTab === 'qrcode' && styles.activeTabText]}>{t('qrCode.QRCode')}</Text>
                </TouchableOpacity>
              </ScrollView>
            </View>


            <ScrollView style={styles.content}>
              <View style={activeTab === 'details' ? {} : { display: 'none' }}>
                {renderOverviewTab()}
              </View>
              <View style={activeTab === 'plants' ? {} : { display: 'none' }}>
                {renderPlantsList()}
              </View>
              <View style={activeTab === 'gallery' ? {} : { display: 'none' }}>
                {renderGalleryTab()}
              </View>
              <View style={activeTab === 'qrcode' ? {} : { display: 'none' }}>
                {renderQRCodeTab()}
              </View>
            </ScrollView>

          </View>
        </ScrollView>

        {/* Delete Confirmation Modal */}
        <Modal
          visible={showDeleteConfirm}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowDeleteConfirm(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.confirmModal}>
              <Text style={styles.confirmTitle}>{t('entity.garden.delete')}</Text>
              <Text style={styles.confirmText}>
                {t('entity.garden.deleteConfirmationMessage')}
              </Text>

              <View style={styles.confirmButtons}>
                <TouchableOpacity
                  style={[styles.confirmButton, styles.cancelButton]}
                  onPress={() => setShowDeleteConfirm(false)}
                >
                  <Text style={styles.cancelButtonText}>{t('common.cancel')}</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.confirmButton, styles.deleteButton]}
                  onPress={handleDelete}
                >
                  <Text style={styles.deleteButtonText}>{t('common.Delete')}</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>

        {/* Image Viewer Modal */}
        <Modal
          visible={showImageModal}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowImageModal(false)}
        >
          <View style={styles.imageModalOverlay}>
            <TouchableOpacity
              style={styles.closeImageButton}
              onPress={() => setShowImageModal(false)}
            >
              <X size={24} color={colors.white} />
            </TouchableOpacity>

            <Image
              source={{ uri: selectedImage }}
              style={styles.fullImage}
              resizeMode="contain"
            />

            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.imageThumbnails}
            >
              {garden.photos && garden.photos.map((photo: any, index: number) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.thumbnailContainer,
                    selectedImage === photo.url && styles.selectedThumbnail
                  ]}
                  onPress={() => setSelectedImage(photo.url)}
                >
                  <Image
                    source={{ uri: photo.url }}
                    style={styles.thumbnail}
                    resizeMode="cover"
                  />
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </Modal>

        {/* QR Code Modal */}
        <Modal
          visible={showQRModal}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowQRModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.qrModal}>
              <View style={styles.qrModalHeader}>
                <Text style={styles.qrModalTitle}>{t('entity.garden.gardenQRCode')}</Text>
                <TouchableOpacity
                  style={styles.closeQrButton}
                  onPress={() => setShowQRModal(false)}
                >
                  <X size={20} color={colors.gray[600]} />
                </TouchableOpacity>
              </View>

              <Text style={styles.qrModalDescription}>
                {t('qrCode.information')} {garden.name}.
              </Text>

              <View style={styles.qrCodeContainer}>
                <QRCodeDisplay
                  value={getQRCodeValue()}
                  size={200}
                  itemType="garden"
                  itemName={garden.name}
                />
              </View>

              <Text style={styles.qrModalInstructions}>
                You can save this QR code to your device or share it with others.
              </Text>
            </View>
          </View>
        </Modal>
        {/* Inactive Status Modal */}
        <InactiveStatusModal
          visible={showInactiveModal}
          onClose={() => setShowInactiveModal(false)}
          onSubmit={handleMarkInactive}
          entityType="garden"
          showCascadeOption={true}
        />
      </SafeAreaView>
      {/* <FinanceBottomSheet
        isVisible={showFinanceBottomSheet}
        onClose={handleCloseFinanceBottomSheet}
        entityId={garden.id}
        entityType="garden"
        bottomSheetRef={financeBottomSheetRef}
      /> */}
    </>
  );
}

// Helper function to get plant status color
const getPlantStatusColor = (status: string) => {
  switch (status) {
    case 'seedling':
      return colors.info;
    case 'growing':
      return colors.success;
    case 'flowering':
      return colors.primary;
    case 'fruiting':
      return colors.warning;
    case 'harvested':
      return colors.secondary;
    case 'dormant':
      return colors.gray[500];
    default:
      return colors.gray[500];
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  tabsContainer: {
    marginBottom: 16,
    backgroundColor: colors.white,
    borderRadius: 12,
    paddingVertical: 8,
    paddingHorizontal: 4,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  tab: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginRight: 8,
  },
  tabs: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
  },
  tabText: {
    fontSize: 14,
    color: colors.gray[600],
    fontWeight: '500',
  },
  activeTabText: {
    color: colors.primary,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  tabContent: {
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: colors.white,
  },
  errorText: {
    fontSize: 18,
    color: colors.gray[700],
    marginBottom: 20,
    textAlign: 'center',
  },
  errorButton: {
    width: 200,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 40,
  },
  imageContainer: {
    position: 'relative',
    height: 200,
  },
  coverImage: {
    width: '100%',
    height: '100%',
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    padding: 16,
  },
  viewImagesButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  quickActionsContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',

    color: colors.gray[800],
    marginBottom: 12,
  },
  quickActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionButton: {
    width: '48%',
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  quickActionIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  quickActionText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[800],
  },
  viewImagesText: {
    color: colors.white,
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  contentContainer: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: -20,
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  titleIcon: {
    marginRight: 12,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: colors.gray[600],
  },
  statusContainer: {
    position: 'relative',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    color: colors.white,
    fontWeight: '500',
    fontSize: 14,
    marginRight: 4,
  },
  statusDropdownContainer: {
    position: 'relative',
  },
  dropdownMenu: {
    position: 'absolute',
    top: '100%',
    right: 0,
    width: 150,
    backgroundColor: colors.white,
    borderRadius: 8,
    shadowColor: colors.gray[900],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    zIndex: 10,
    marginTop: 4,
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[100],
  },
  dropdownItemText: {
    fontSize: 14,
    color: colors.gray[800],
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  infoSection: {
    backgroundColor: colors.gray[50],
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
  },
  infoIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  infoLabel: {
    fontSize: 12,
    color: colors.gray[500],
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[800],
  },
  irrigationContainer: {
    flex: 1,
  },
  irrigationLabel: {
    fontSize: 12,
    color: colors.gray[500],
    marginBottom: 4,
  },
  irrigationInput: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    padding: 8,
    fontSize: 14,
    color: colors.gray[800],
  },
  soilTypeContainer: {
    marginTop: 8,
  },
  soilTypeLabel: {
    fontSize: 12,
    color: colors.gray[500],
    marginBottom: 4,
  },
  soilTypeBadge: {
    backgroundColor: colors.gray[200],
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: 'flex-start',
  },
  soilTypeText: {
    color: colors.gray[800],
    fontWeight: '500',
    fontSize: 14,
  },
  notesSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 12,
  },
  notesInput: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    color: colors.gray[800],
    minHeight: 100,
    textAlignVertical: 'top',
  },
  plantsSection: {
    marginBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  addPlantButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  addPlantText: {
    color: colors.white,
    fontWeight: '500',
    fontSize: 14,
  },
  plantsList: {
    marginBottom: 16,
  },
  plantCard: {
    flexDirection: 'row',
    backgroundColor: colors.gray[50],
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 12,
  },
  plantImage: {
    width: 80,
    height: 80,
  },
  plantInfo: {
    flex: 1,
    padding: 12,
  },
  plantName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 4,
  },
  plantSpecies: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 8,
  },
  plantStatusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  plantStatusText: {
    color: colors.white,
    fontSize: 12,
    fontWeight: '500',
  },
  noPlants: {
    padding: 20,
    backgroundColor: colors.gray[50],
    borderRadius: 12,
    alignItems: 'center',
  },
  noPlantsText: {
    fontSize: 14,
    color: colors.gray[600],
    textAlign: 'center',
  },
  photosSection: {
    marginBottom: 20,
  },
  photosContainer: {
    paddingBottom: 16,
  },
  photoItem: {
    marginRight: 12,
    width: 120,
    height: 120,
    borderRadius: 8,
    overflow: 'hidden',
  },
  photoImage: {
    width: '100%',
    height: '100%',
  },
  photoDate: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    color: colors.white,
    fontSize: 10,
    padding: 4,
    textAlign: 'center',
  },
  noPhotosContainer: {
    width: 120,
    height: 120,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
  },
  noPhotosText: {
    fontSize: 12,
    color: colors.gray[500],
    marginTop: 8,
    textAlign: 'center',
  },
  addPhotoContainer: {
    marginBottom: 16,
  },
  addPhotoButton: {
    marginTop: 12,
  },
  headerButtons: {
    flexDirection: 'row',
  },
  headerButton: {
    marginLeft: 16,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  confirmModal: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 20,
    width: '80%',
    maxWidth: 400,
  },
  confirmTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 12,
  },
  confirmText: {
    fontSize: 14,
    color: colors.gray[700],
    marginBottom: 20,
  },
  confirmButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  confirmButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginLeft: 12,
  },
  cancelButton: {
    backgroundColor: colors.gray[200],
  },
  cancelButtonText: {
    color: colors.gray[800],
    fontWeight: '500',
  },
  deleteButton: {
    backgroundColor: colors.danger,
  },
  deleteButtonText: {
    color: colors.white,
    fontWeight: '500',
  },
  imageModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeImageButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    zIndex: 10,
  },
  fullImage: {
    width: '100%',
    height: '70%',
  },
  imageThumbnails: {
    position: 'absolute',
    bottom: 20,
    paddingHorizontal: 20,
  },
  thumbnailContainer: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginHorizontal: 8,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedThumbnail: {
    borderColor: colors.primary,
  },
  thumbnail: {
    width: '100%',
    height: '100%',
  },
  qrCodeSection: {
    backgroundColor: colors.gray[50],
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  qrCodeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  qrCodeDescription: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 8,
  },
  viewQRButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primaryLight,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  viewQRIcon: {
    marginRight: 6,
  },
  viewQRText: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '500',
  },
  qrModal: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 24,
    width: '90%',
    maxWidth: 400,
    alignItems: 'center',
  },
  qrModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: 16,
  },
  qrModalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
  },
  closeQrButton: {
    padding: 4,
  },
  qrModalDescription: {
    fontSize: 14,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: 24,
  },
  qrCodeContainer: {
    marginBottom: 24,
  },
  qrModalInstructions: {
    fontSize: 14,
    color: colors.gray[600],
    textAlign: 'center',
  },
  inactiveBanner: {
    backgroundColor: colors.gray[700],
    padding: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  inactiveBannerText: {
    color: colors.white,
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '500',
  },
  inactiveDetailsContainer: {
    marginTop: 16,
    padding: 16,
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: colors.gray[500],
  },
  inactiveDetailsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 12,
  },
  inactiveDetailsRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  inactiveDetailsLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
    width: 80,
  },
  inactiveDetailsValue: {
    fontSize: 14,
    color: colors.gray[800],
    flex: 1,
  },
  inactiveImageContainer: {
    marginTop: 8,
  },
  inactiveImage: {
    width: '100%',
    height: 150,
    borderRadius: 8,
    marginTop: 8,
  },
  disabledActionButton: {
    opacity: 0.6,
  },
  disabledActionText: {
    color: colors.gray[500],
  },
  imageSliderContainer: {
    marginBottom: 16,
  },
});











