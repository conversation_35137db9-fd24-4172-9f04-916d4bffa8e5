import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  Image,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { colors } from '@/constants/colors';
import { useTranslation } from '@/i18n/useTranslation';
import { EntityListItem } from '@/services/ai-assistant';

interface ChatEntityListProps {
  entities: EntityListItem[];
  onEntityPress?: (entity: EntityListItem) => void;
  onViewDetails?: (entity: EntityListItem) => void;
}

export default function ChatEntityList({ 
  entities, 
  onEntityPress, 
  onViewDetails 
}: ChatEntityListProps) {
  const { t, isRTL } = useTranslation();

  const getEntityIcon = (type: string) => {
    switch (type) {
      case 'animal':
        return 'paw';
      case 'plant':
        return 'leaf';
      case 'field':
        return 'grid';
      case 'garden':
        return 'flower';
      case 'equipment':
        return 'construct';
      default:
        return 'ellipse';
    }
  };

  const getStatusColor = (status?: string) => {
    if (!status) return colors.gray[400];
    
    switch (status.toLowerCase()) {
      case 'active':
      case 'healthy':
      case 'excellent':
      case 'good':
        return colors.success;
      case 'inactive':
      case 'poor':
      case 'needs attention':
        return colors.danger;
      case 'fair':
      case 'maintenance':
        return colors.warning;
      default:
        return colors.gray[400];
    }
  };

  const handleEntityPress = (entity: EntityListItem) => {
    if (onEntityPress) {
      onEntityPress(entity);
    } else {
      // Default navigation to detail screen
      router.push(`/${entity.type}/${entity.id}`);
    }
  };

  const handleViewDetails = (entity: EntityListItem) => {
    if (onViewDetails) {
      onViewDetails(entity);
    } else {
      router.push(`/${entity.type}/${entity.id}`);
    }
  };

  const renderEntityItem = ({ item }: { item: EntityListItem }) => (
    <TouchableOpacity
      style={[styles.entityCard, isRTL && { flexDirection: 'row-reverse' }]}
      onPress={() => handleEntityPress(item)}
      activeOpacity={0.7}
    >
      <View style={styles.entityImageContainer}>
        {item.image ? (
          <Image
            source={{ uri: item.image }}
            style={styles.entityImage}
            resizeMode="cover"
          />
        ) : (
          <View style={[styles.entityImagePlaceholder, { backgroundColor: colors.gray[100] }]}>
            <Ionicons
              name={getEntityIcon(item.type) as any}
              size={24}
              color={colors.gray[500]}
            />
          </View>
        )}
      </View>

      <View style={[styles.entityInfo, isRTL && { alignItems: 'flex-end' }]}>
        <Text style={[styles.entityName, isRTL && { textAlign: 'right' }]}>
          {item.name}
        </Text>
        <Text style={[styles.entityType, isRTL && { textAlign: 'right' }]}>
          {t(`entity.${item.type}.title`, item.type)}
        </Text>
        <Text style={[styles.entitySummary, isRTL && { textAlign: 'right' }]}>
          {item.summary}
        </Text>
        {item.status && (
          <View style={[styles.statusContainer, isRTL && { flexDirection: 'row-reverse' }]}>
            <View
              style={[
                styles.statusDot,
                { backgroundColor: getStatusColor(item.status) }
              ]}
            />
            <Text style={styles.statusText}>{item.status}</Text>
          </View>
        )}
      </View>

      <TouchableOpacity
        style={styles.detailsButton}
        onPress={() => handleViewDetails(item)}
      >
        <Ionicons
          name={isRTL ? 'chevron-back' : 'chevron-forward'}
          size={20}
          color={colors.primary}
        />
      </TouchableOpacity>
    </TouchableOpacity>
  );

  if (!entities || entities.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Ionicons name="search" size={40} color={colors.gray[400]} />
        <Text style={styles.emptyText}>
          {t('ai.noEntitiesFound', 'No entities found')}
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={[styles.title, isRTL && { textAlign: 'right' }]}>
        {t('ai.entityList', 'Your Farm Entities')} ({entities.length})
      </Text>
      <FlatList
        data={entities}
        renderItem={renderEntityItem}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContainer}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 12,
  },
  listContainer: {
    paddingBottom: 8,
  },
  entityCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 12,
    borderWidth: 1,
    borderColor: colors.gray[200],
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  entityImageContainer: {
    marginRight: 12,
  },
  entityImage: {
    width: 50,
    height: 50,
    borderRadius: 8,
  },
  entityImagePlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  entityInfo: {
    flex: 1,
  },
  entityName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 2,
  },
  entityType: {
    fontSize: 12,
    color: colors.primary,
    textTransform: 'capitalize',
    marginBottom: 4,
  },
  entitySummary: {
    fontSize: 14,
    color: colors.text.secondary,
    marginBottom: 6,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 12,
    color: colors.text.secondary,
    textTransform: 'capitalize',
  },
  detailsButton: {
    padding: 8,
  },
  separator: {
    height: 8,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 16,
    color: colors.text.secondary,
    marginTop: 12,
    textAlign: 'center',
  },
});
