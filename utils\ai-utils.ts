import { TransactionCategory } from '@/types';
import * as FileSystem from 'expo-file-system';

/**
 * Analyzes a transaction description using AI to classify it
 * In a real app, this would call an AI service like OpenAI
 */
export const analyzeTransactionWithAI = async (description: string): Promise<{
  category: TransactionCategory;
  confidence: number;
  tags?: string[];
}> => {
  // This is a mock implementation
  // In a real app, this would call an AI service

  description = description.toLowerCase();

  if (description.includes('seed') || description.includes('planting') || description.includes('sowing')) {
    return {
      category: 'seed_cost',
      confidence: 0.92,
      tags: ['seeds', 'planting']
    };
  }

  if (description.includes('water') || description.includes('irrigation') || description.includes('sprinkler')) {
    return {
      category: 'irrigation',
      confidence: 0.89,
      tags: ['water', 'irrigation']
    };
  }

  if (description.includes('pesticide') || description.includes('insecticide') || description.includes('herbicide') || description.includes('fungicide')) {
    return {
      category: 'pesticide',
      confidence: 0.95,
      tags: ['pest control', 'chemicals']
    };
  }

  if (description.includes('fertilizer') || description.includes('nutrient') || description.includes('compost')) {
    return {
      category: 'fertilizer',
      confidence: 0.91,
      tags: ['nutrients', 'soil health']
    };
  }

  if (description.includes('worker') || description.includes('labor') || description.includes('staff') || description.includes('wage')) {
    return {
      category: 'labor',
      confidence: 0.88,
      tags: ['workers', 'manual labor']
    };
  }

  if (description.includes('tractor') || description.includes('equipment') || description.includes('machinery') || description.includes('tool')) {
    return {
      category: 'equipment',
      confidence: 0.87,
      tags: ['machinery', 'tools']
    };
  }

  if (description.includes('sale') || description.includes('sold') || description.includes('revenue') || description.includes('harvest sale')) {
    return {
      category: 'harvest_sale',
      confidence: 0.94,
      tags: ['sales', 'revenue']
    };
  }

  // Default case
  return {
    category: 'other',
    confidence: 0.6,
    tags: ['miscellaneous']
  };
};

// const getPromptByCategory = (category: 'animal' | 'plant' | 'equipment' | 'field' | 'garden' | 'yield') => {
//   switch (category) {
//     case 'animal':
//       return 'Identify the animal species or breed. Include its category (e.g., meat, dairy, poultry), health status if visible, and an age estimate.';
//     case 'plant':
//       return 'Identify the plant species. Mention its growth stage, and any visible diseases or deficiencies.';
//     case 'equipment':
//       return 'Identify the type of equipment and check for visible damage, missing parts, or wear.';
//     case 'field':
//       return 'Analyze the field. Describe the crop type, coverage, soil condition, and any visible issues like weeds or stress.';
//     case 'garden':
//       return 'Analyze the garden. Describe the types of plants, soil condition, and overall health.';
//     case 'yield':
//       return 'Analyze the harvested produce. Identify the crop, estimate its quality, and note any visible damage or ripeness issues.';
//     default:
//       return 'Identify what is shown in this image and describe it in detail.';
//   }
// };

// const getPromptDetailsByCategory = (category: 'animal' | 'plant' | 'equipment' | 'field' | 'garden' | 'yield') => {
//   switch (category) {
//     case 'animal':
//       return 'For the "details" object, include these keys: "species" (string), "breed" (string), "ageEstimate" (string, e.g., "juvenile", "adult"), "healthIndicators" (string, describing visible health), "category" (string, e.g., "dairy", "meat", "work").';
//     case 'plant':
//       return 'For the "details" object, include these keys: "species" (string), "growthStage" (string, e.g., "germination", "flowering", "fruiting"), "healthStatus" (string, describing diseases or deficiencies), "yieldReadiness" (string, e.g., "not ready", "ready for harvest").';
//     case 'equipment':
//       return 'For the "details" object, include these keys: "type" (string, e.g., "tractor", "sprayer"), "condition" (string, e.g., "clean", "worn", "damaged"), "partsCheck" (string, describing missing parts or issues), "usageCategory" (string, e.g., "harvesting", "irrigation").';
//     case 'field':
//       return 'For the "details" object, include these keys: "crop" (string), "coverage" (string), "soil" (string), "issues" (string).';
//     case 'garden':
//       return 'For the "details" object, include these keys: "plantTypes" (string), "soilCondition" (string), "overallHealth" (string).';
//     case 'yield':
//       return 'For the "details" object, include these keys: "crop" (string), "quality" (string), "ripeness" (string).';
//     default:
//       return 'For the "details" object, include any relevant key-value pairs you can identify.';
//   }
// };


const getPromptByCategory = (category: 'animal' | 'plant' | 'equipment' | 'field' | 'garden' | 'yield') => {
  switch (category) {
    case 'animal':
      return 'Identify the animal in detail. Provide the species, breed, gender, estimated age, and health status based on visual cues. Look for any signs of illness, injury, or distress.';
    case 'plant':
      return 'Identify the plant in detail. Provide the species, variety, growth stage, and health status. Note any signs of disease, nutrient deficiency, or pest damage.';
    case 'equipment':
      return 'Identify the type of equipment and check for visible damage, missing parts, or wear.';
    case 'field':
      return 'Analyze the field. Describe the crop type, coverage, soil condition, and any visible issues like weeds or stress.';
    case 'garden':
      return 'Analyze the garden. Describe the types of plants, soil condition, and overall health.';
    case 'yield':
      return 'Analyze the harvested produce. Identify the crop, estimate its quality, and note any visible damage or ripeness issues.';
    default:
      return 'Identify what is shown in this image and describe it in detail.';
  }
};

const getPromptDetailsByCategory = (category: 'animal' | 'plant' | 'equipment' | 'field' | 'garden' | 'yield') => {
  switch (category) {
    case 'animal':
      return 'For the "details" object, include these specific keys: "species" (string, e.g., "Cattle", "Sheep"), "breed" (string, e.g., "Holstein", "Angus"), "gender" (string, "male", "female", or "unknown"), "ageEstimate" (string, e.g., "calf", "juvenile", "adult", "senior"), "healthStatus" (string, describing visible health conditions), "bodyCondition" (string, e.g., "thin", "ideal", "overweight"), "notes" (string, any additional observations).';
    case 'plant':
      return 'For the "details" object, include these specific keys: "species" (string, e.g., "Tomato", "Wheat"), "variety" (string, e.g., "Roma", "Cherry"), "growthStage" (string, e.g., "seedling", "vegetative", "flowering", "fruiting"), "healthStatus" (string, e.g., "healthy", "stressed", "diseased"), "issues" (string, describing any visible problems), "soilCondition" (string, if visible), "notes" (string, any additional observations).';
    case 'equipment':
      return 'For the "details" object, include these keys: "type" (string, e.g., "tractor", "sprayer"), "brand" (string, if identifiable), "model" (string, if identifiable), "condition" (string, e.g., "excellent", "good", "fair", "poor"), "issues" (string, describing any visible problems), "estimatedAge" (string), "notes" (string, any additional observations).';
    case 'field':
      return 'For the "details" object, include these keys: "cropType" (string), "growthStage" (string), "coverage" (string, e.g., "sparse", "moderate", "dense"), "soilCondition" (string), "issues" (string, describing weeds, pests, or other problems), "irrigationStatus" (string, if visible), "notes" (string, any additional observations).';
    case 'garden':
      return 'For the "details" object, include these keys: "plantTypes" (string, list main plants visible), "layout" (string, e.g., "rows", "beds", "mixed"), "soilCondition" (string), "overallHealth" (string), "issues" (string, describing any problems), "notes" (string, any additional observations).';
    case 'yield':
      return 'For the "details" object, include these keys: "cropType" (string), "quality" (string, e.g., "excellent", "good", "fair", "poor"), "quantity" (string, estimate if possible), "ripeness" (string), "issues" (string, describing any damage or defects), "notes" (string, any additional observations).';
    default:
      return 'For the "details" object, include any relevant key-value pairs you can identify.';
  }
};
/**
 * Analyzes an image for form auto-population
 * @param imageUri The URI of the image to analyze.
 * @param category The category of the entity in the image.
 * @returns A structured analysis object with form field suggestions.
 */
export const analyzeImageForFormPopulation = async (
  imageUri: string,
  category: 'animal' | 'plant' | 'equipment'
): Promise<{
  analysis: string;
  suggestedFields: Record<string, any>;
  confidence: number;
}> => {
  const apiKey = '********************************************************************************************************************************************************************';

  if (!apiKey) {
    throw new Error("OpenAI API key is missing");
  }

  const prompts = {
    plant: `Analyze this plant image and extract the following information in JSON format:
    {
      "species": "scientific or common name of the plant species",
      "variety": "specific variety if identifiable",
      "health": "healthy/sick/diseased/pest_damage",
      "growth_stage": "seedling/young/mature/flowering/fruiting",
      "estimated_age": "approximate age in days/weeks/months",
      "care_notes": "any visible care requirements or issues"
    }
    Provide your best estimates based on visual analysis. Use "unknown" for fields that cannot be determined.`,

    animal: `Analyze this animal image and extract the following information in JSON format:
    {
      "species": "animal species (cow, goat, chicken, etc.)",
      "breed": "specific breed if identifiable",
      "gender": "male/female/unknown",
      "estimated_age": "approximate age category (young/adult/old)",
      "estimated_weight": "estimated weight range in kg",
      "health_status": "healthy/sick/injured/pregnant",
      "physical_condition": "excellent/good/fair/poor",
      "distinctive_features": "any notable markings or features"
    }
    Provide your best estimates based on visual analysis. Use "unknown" for fields that cannot be determined.`,

    equipment: `Analyze this equipment/machinery image and extract the following information in JSON format:
    {
      "type": "tractor/harvester/plow/sprayer/irrigation/tool/other",
      "manufacturer": "brand name if visible",
      "model": "model name/number if visible",
      "condition": "excellent/good/fair/poor/needs_repair",
      "estimated_age": "approximate age category (new/used/old)",
      "key_features": "notable features or attachments visible",
      "maintenance_needs": "any visible maintenance requirements"
    }
    Provide your best estimates based on visual analysis. Use "unknown" for fields that cannot be determined.`
  };

  try {
    const base64Image = await FileSystem.readAsStringAsync(imageUri, {
      encoding: FileSystem.EncodingType.Base64,
    });

    const payload = {
      model: "gpt-4o",
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: prompts[category] },
            {
              type: "image_url",
              image_url: { url: `data:image/jpeg;base64,${base64Image}` },
            },
          ],
        },
      ],
      max_tokens: 500,
      response_format: { type: "json_object" },
    };

    const response = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${apiKey}`,
      },
      body: JSON.stringify(payload),
    });

    const responseData = await response.json();

    if (!response.ok) {
      console.error("OpenAI API Error:", responseData);
      throw new Error(responseData.error?.message || "Failed to analyze image with AI.");
    }

    const content = responseData.choices[0].message.content;
    const parsedContent = JSON.parse(content);

    return {
      analysis: `AI analyzed the ${category} image and extracted relevant information.`,
      suggestedFields: parsedContent,
      confidence: 0.8 // Default confidence score
    };
  } catch (error) {
    console.error('Error analyzing image for form population:', error);
    throw error;
  }
};

/**
 * Analyzes an image using the OpenAI Vision API.
 * @param imageUri The URI of the image to analyze.
 * @param category The category of the entity in the image.
 * @returns A structured analysis object.
 */
export const analyzeImageWithAI = async (
  imageUri: string,
  category: 'animal' | 'plant' | 'equipment' | 'field' | 'garden' | 'yield'
): Promise<{
  analysis: string;
  details: Record<string, any>;
}> => {
  const apiKey = '********************************************************************************************************************************************************************'; // process.env.EXPO_PUBLIC_OPENAI_API_KEY;
  if (!apiKey) {
    console.error("OpenAI API key is not set in environment variables.");
    throw new Error("OpenAI API key is missing. Please set EXPO_PUBLIC_OPENAI_API_KEY in your .env file.");
  }

  try {
    const base64Image = await FileSystem.readAsStringAsync(imageUri, {
      encoding: FileSystem.EncodingType.Base64,
    });

    // const userPrompt = getPromptByCategory(category);
    // const detailsInstruction = getPromptDetailsByCategory(category);
    // const systemPrompt = `You are an expert agricultural assistant. Analyze the following image based on the user's request. Respond ONLY with a valid JSON object with two keys: "analysis" (a human-readable summary string) and "details" (a JSON object with specific key-value pairs relevant to the category). ${detailsInstruction} User's request: "${userPrompt}"`;
    const userPrompt = getPromptByCategory(category);
    const detailsInstruction = getPromptDetailsByCategory(category);
    const systemPrompt = `You are an expert agricultural assistant specializing in ${category} analysis. Analyze the following image based on the user's request. Respond ONLY with a valid JSON object with two keys: "analysis" (a concise human-readable summary string) and "details" (a JSON object with specific key-value pairs relevant to the category). ${detailsInstruction} User's request: "${userPrompt}"`;
    const payload = {
      model: "gpt-4o",
      messages: [
        {
          role: "user",
          content: [
            { type: "text", text: systemPrompt },
            {
              type: "image_url",
              image_url: { url: `data:image/jpeg;base64,${base64Image}` },
            },
          ],
        },
      ],
      max_tokens: 500,
      response_format: { type: "json_object" },
    };

    const response = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${apiKey}`,
      },
      body: JSON.stringify(payload),
    });

    const responseData = await response.json();

    if (!response.ok) {
      console.error("OpenAI API Error:", responseData);
      throw new Error(responseData.error?.message || "Failed to analyze image with AI.");
    }

    const content = responseData.choices[0].message.content;
    const parsedContent = JSON.parse(content);
    // console.log({ parsedContent })
    return {
      analysis: parsedContent.analysis || "Analysis not available.",
      details: parsedContent.details || {},
    };
  } catch (error) {
    console.error("Error in analyzeImageWithAI:", error);
    throw new Error("An error occurred while analyzing the image.");
  }
};