import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
} from 'react-native';
import { colors } from '@/constants/colors';
import { useTranslation } from '@/i18n/useTranslation';
import {
  Tractor,
  Wrench,
  Package,
  Calendar,
  DollarSign,
  MapPin,
  AlertTriangle,
  CheckCircle,
  Clock,
  User,
} from 'lucide-react-native';

interface EquipmentItemCardProps {
  item: {
    id: string;
    name: string;
    category: string;
    quantity: number;
    unit: string;
    price?: number;
    supplier?: string;
    location?: string;
    description?: string;
    imageUrl?: string;
    isConsumable?: boolean;
    minQuantity?: number;
    purchaseDate?: string;
    expiryDate?: string;
    history?: any[];
    createdAt?: string;
    updatedAt?: string;
  };
  onPress: () => void;
}

export default function EquipmentItemCard({ item, onPress }: EquipmentItemCardProps) {
  const { t, isRTL } = useTranslation();

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Equipment':
        return <Tractor size={20} color={colors.secondary} />;
      case 'Tools':
        return <Wrench size={20} color={colors.primary} />;
      default:
        return <Package size={20} color={colors.gray[500]} />;
    }
  };

  const getStatusColor = () => {
    if (item.quantity <= (item.minQuantity || 0)) {
      return colors.danger;
    }
    if (item.expiryDate && new Date(item.expiryDate) < new Date()) {
      return colors.warning;
    }
    return colors.success;
  };

  const getStatusIcon = () => {
    if (item.quantity <= (item.minQuantity || 0)) {
      return <AlertTriangle size={16} color={colors.danger} />;
    }
    if (item.expiryDate && new Date(item.expiryDate) < new Date()) {
      return <Clock size={16} color={colors.warning} />;
    }
    return <CheckCircle size={16} color={colors.success} />;
  };

  const getStatusText = () => {
    if (item.quantity <= (item.minQuantity || 0)) {
      return t('equipment.status.lowStock', 'Low Stock');
    }
    if (item.expiryDate && new Date(item.expiryDate) < new Date()) {
      return t('equipment.status.expired', 'Expired');
    }
    return t('equipment.status.available', 'Available');
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  return (
    <TouchableOpacity
      style={[styles.card, isRTL && { flexDirection: 'row-reverse' }]}
      onPress={onPress}
    >
      {/* Image Section */}
      <View style={styles.imageContainer}>
        {item.imageUrl ? (
          <Image
            source={{ uri: item.imageUrl }}
            style={styles.image}
            resizeMode="cover"
          />
        ) : (
          <View style={styles.imagePlaceholder}>
            {getCategoryIcon(item.category)}
          </View>
        )}
      </View>

      {/* Content Section */}
      <View style={[styles.content, isRTL && { alignItems: 'flex-end' }]}>
        {/* Header */}
        <View style={[styles.header, isRTL && { flexDirection: 'row-reverse' }]}>
          <Text style={[styles.name, isRTL && { textAlign: 'right' }]} numberOfLines={1}>
            {item.name}
          </Text>
          <View style={styles.statusContainer}>
            {getStatusIcon()}
          </View>
        </View>

        {/* Category */}
        <View style={[styles.categoryRow, isRTL && { flexDirection: 'row-reverse' }]}>
          {getCategoryIcon(item.category)}
          <Text style={[styles.categoryText, isRTL && { marginRight: 4, marginLeft: 0 }]}>
            {item.category}
          </Text>
          {item.isConsumable && (
            <View style={styles.consumableBadge}>
              <Text style={styles.consumableBadgeText}>
                {t('equipment.consumable', 'Consumable')}
              </Text>
            </View>
          )}
        </View>

        {/* Status */}
        <View style={[styles.statusRow, isRTL && { flexDirection: 'row-reverse' }]}>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor() + '20' }]}>
            <Text style={[styles.statusText, { color: getStatusColor() }]}>
              {getStatusText()}
            </Text>
          </View>
        </View>

        {/* Details Grid */}
        <View style={styles.detailsGrid}>
          <View style={[styles.detailRow, isRTL && { flexDirection: 'row-reverse' }]}>
            <Package size={14} color={colors.gray[500]} />
            <Text style={[styles.detailText, isRTL && { marginRight: 4, marginLeft: 0 }]}>
              {item.quantity} {item.unit}
            </Text>
          </View>

          {item.price && (
            <View style={[styles.detailRow, isRTL && { flexDirection: 'row-reverse' }]}>
              <DollarSign size={14} color={colors.gray[500]} />
              <Text style={[styles.detailText, isRTL && { marginRight: 4, marginLeft: 0 }]}>
                ${item.price}
              </Text>
            </View>
          )}

          {item.supplier && (
            <View style={[styles.detailRow, isRTL && { flexDirection: 'row-reverse' }]}>
              <User size={14} color={colors.gray[500]} />
              <Text style={[styles.supplierText, isRTL && { marginRight: 4, marginLeft: 0 }]} numberOfLines={1}>
                {item.supplier}
              </Text>
            </View>
          )}

          {item.purchaseDate && (
            <View style={[styles.detailRow, isRTL && { flexDirection: 'row-reverse' }]}>
              <Calendar size={14} color={colors.gray[500]} />
              <Text style={[styles.detailText, isRTL && { marginRight: 4, marginLeft: 0 }]}>
                {formatDate(item.purchaseDate)}
              </Text>
            </View>
          )}
        </View>

        {/* Description */}
        {item.description && item.description !== 'No description' && (
          <Text style={[styles.description, isRTL && { textAlign: 'right' }]} numberOfLines={2}>
            {item.description}
          </Text>
        )}

        {/* Expiry Warning */}
        {item.expiryDate && (
          <View style={[styles.expiryRow, isRTL && { flexDirection: 'row-reverse' }]}>
            <Clock size={12} color={colors.warning} />
            <Text style={[styles.expiryText, isRTL && { marginRight: 4, marginLeft: 0 }]}>
              {t('equipment.expiresOn', 'Expires on')} {formatDate(item.expiryDate)}
            </Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: colors.gray[800],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  imageContainer: {
    marginRight: 12,
  },
  image: {
    width: 64,
    height: 64,
    borderRadius: 8,
  },
  imagePlaceholder: {
    width: 64,
    height: 64,
    borderRadius: 8,
    backgroundColor: colors.gray[100],
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 6,
  },
  name: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    flex: 1,
  },
  statusContainer: {
    marginLeft: 8,
  },
  categoryRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  categoryText: {
    fontSize: 12,
    color: colors.gray[600],
    marginLeft: 4,
    fontWeight: '500',
  },
  consumableBadge: {
    backgroundColor: colors.info + '20',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginLeft: 8,
  },
  consumableBadgeText: {
    fontSize: 10,
    color: colors.info,
    fontWeight: '500',
  },
  statusRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  statusText: {
    fontSize: 11,
    fontWeight: '500',
  },
  detailsGrid: {
    marginBottom: 8,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 3,
  },
  detailText: {
    fontSize: 12,
    color: colors.gray[600],
    marginLeft: 4,
  },
  supplierText: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '500',
    marginLeft: 4,
    flex: 1,
  },
  description: {
    fontSize: 12,
    color: colors.gray[500],
    lineHeight: 16,
    marginBottom: 6,
  },
  expiryRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  expiryText: {
    fontSize: 11,
    color: colors.warning,
    marginLeft: 4,
    fontWeight: '500',
  },
});
