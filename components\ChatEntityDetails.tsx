import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { colors } from '@/constants/colors';
import { useTranslation } from '@/i18n/useTranslation';

interface ChatEntityDetailsProps {
  entityId: string;
  entityType: string;
  data: any;
  onViewFullDetails?: () => void;
  onUpdate?: () => void;
}

export default function ChatEntityDetails({
  entityId,
  entityType,
  data,
  onViewFullDetails,
  onUpdate,
}: ChatEntityDetailsProps) {
  const { t, isRTL } = useTranslation();

  const getEntityIcon = (type: string) => {
    switch (type) {
      case 'animal':
        return 'paw';
      case 'plant':
        return 'leaf';
      case 'field':
        return 'grid';
      case 'garden':
        return 'flower';
      case 'equipment':
        return 'construct';
      default:
        return 'ellipse';
    }
  };

  const getStatusColor = (status?: string) => {
    if (!status) return colors.gray[400];
    
    switch (status.toLowerCase()) {
      case 'active':
      case 'healthy':
      case 'excellent':
      case 'good':
        return colors.success;
      case 'inactive':
      case 'poor':
      case 'needs attention':
        return colors.danger;
      case 'fair':
      case 'maintenance':
        return colors.warning;
      default:
        return colors.gray[400];
    }
  };

  const handleViewFullDetails = () => {
    if (onViewFullDetails) {
      onViewFullDetails();
    } else {
      router.push(`/${entityType}/${entityId}`);
    }
  };

  const handleUpdate = () => {
    if (onUpdate) {
      onUpdate();
    }
  };

  const renderDetailRow = (label: string, value: any, icon?: string) => {
    if (!value) return null;

    return (
      <View style={[styles.detailRow, isRTL && { flexDirection: 'row-reverse' }]}>
        {icon && (
          <Ionicons
            name={icon as any}
            size={16}
            color={colors.gray[500]}
            style={[styles.detailIcon, isRTL && { marginLeft: 8, marginRight: 0 }]}
          />
        )}
        <Text style={[styles.detailLabel, isRTL && { textAlign: 'right' }]}>
          {label}:
        </Text>
        <Text style={[styles.detailValue, isRTL && { textAlign: 'right' }]}>
          {typeof value === 'string' ? value : JSON.stringify(value)}
        </Text>
      </View>
    );
  };

  const renderEntitySpecificDetails = () => {
    switch (entityType) {
      case 'animal':
        return (
          <>
            {renderDetailRow(t('entity.animal.species', 'Species'), data.species, 'paw')}
            {renderDetailRow(t('entity.animal.breed', 'Breed'), data.breed)}
            {renderDetailRow(t('entity.animal.gender', 'Gender'), data.gender)}
            {renderDetailRow(t('entity.animal.birthDate', 'Birth Date'), data.birthDate, 'calendar')}
            {renderDetailRow(t('entity.animal.purpose', 'Purpose'), data.purpose)}
          </>
        );
      case 'plant':
        return (
          <>
            {renderDetailRow(t('entity.plant.species', 'Species'), data.species, 'leaf')}
            {renderDetailRow(t('entity.plant.variety', 'Variety'), data.variety)}
            {renderDetailRow(t('entity.plant.plantedDate', 'Planted Date'), data.plantedDate, 'calendar')}
            {renderDetailRow(t('entity.plant.health', 'Health'), data.health)}
            {renderDetailRow(t('entity.plant.expectedHarvestDate', 'Expected Harvest'), data.expectedHarvestDate)}
          </>
        );
      case 'field':
        return (
          <>
            {renderDetailRow(t('entity.field.type', 'Type'), data.type, 'grid')}
            {renderDetailRow(t('entity.field.size', 'Size'), `${data.size} ${data.sizeUnit}`, 'resize')}
            {renderDetailRow(t('entity.field.soilType', 'Soil Type'), data.soilType)}
            {renderDetailRow(t('entity.field.cropType', 'Crop Type'), data.cropType)}
          </>
        );
      case 'garden':
        return (
          <>
            {renderDetailRow(t('entity.garden.type', 'Type'), data.type || data.gardenType, 'flower')}
            {renderDetailRow(t('entity.garden.size', 'Size'), `${data.size} ${data.sizeUnit}`, 'resize')}
            {renderDetailRow(t('entity.garden.soilType', 'Soil Type'), data.soilType)}
            {renderDetailRow(t('entity.garden.irrigationSystem', 'Irrigation'), data.irrigationSystem)}
          </>
        );
      case 'equipment':
        return (
          <>
            {renderDetailRow(t('entity.equipment.type', 'Type'), data.type, 'construct')}
            {renderDetailRow(t('entity.equipment.manufacturer', 'Manufacturer'), data.manufacturer)}
            {renderDetailRow(t('entity.equipment.model', 'Model'), data.model)}
            {renderDetailRow(t('entity.equipment.purchaseDate', 'Purchase Date'), data.purchaseDate, 'calendar')}
            {renderDetailRow(t('entity.equipment.lastMaintenanceDate', 'Last Maintenance'), data.lastMaintenanceDate)}
          </>
        );
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={[styles.header, isRTL && { flexDirection: 'row-reverse' }]}>
        <View style={styles.headerLeft}>
          {data.image ? (
            <Image
              source={{ uri: data.image }}
              style={styles.entityImage}
              resizeMode="cover"
            />
          ) : (
            <View style={styles.entityImagePlaceholder}>
              <Ionicons
                name={getEntityIcon(entityType) as any}
                size={24}
                color={colors.gray[500]}
              />
            </View>
          )}
          <View style={[styles.entityInfo, isRTL && { alignItems: 'flex-end' }]}>
            <Text style={[styles.entityName, isRTL && { textAlign: 'right' }]}>
              {data.name || data.species || `${entityType} ${entityId}`}
            </Text>
            <Text style={[styles.entityType, isRTL && { textAlign: 'right' }]}>
              {t(`entity.${entityType}.title`, entityType)}
            </Text>
            {data.status && (
              <View style={[styles.statusContainer, isRTL && { flexDirection: 'row-reverse' }]}>
                <View
                  style={[
                    styles.statusDot,
                    { backgroundColor: getStatusColor(data.status) }
                  ]}
                />
                <Text style={styles.statusText}>{data.status}</Text>
              </View>
            )}
          </View>
        </View>
      </View>

      {/* Details */}
      <ScrollView style={styles.detailsContainer} showsVerticalScrollIndicator={false}>
        {renderEntitySpecificDetails()}
        {data.notes && renderDetailRow(t('common.notes', 'Notes'), data.notes, 'document-text')}
        {data.location && renderDetailRow(t('common.location', 'Location'), data.location, 'location')}
      </ScrollView>

      {/* Action Buttons */}
      <View style={[styles.actionButtons, isRTL && { flexDirection: 'row-reverse' }]}>
        <TouchableOpacity
          style={[styles.actionButton, styles.primaryButton]}
          onPress={handleViewFullDetails}
        >
          <Ionicons name="eye" size={16} color={colors.white} />
          <Text style={styles.primaryButtonText}>
            {t('ai.viewFullDetails', 'View Full Details')}
          </Text>
        </TouchableOpacity>
        
        {onUpdate && (
          <TouchableOpacity
            style={[styles.actionButton, styles.secondaryButton]}
            onPress={handleUpdate}
          >
            <Ionicons name="create" size={16} color={colors.primary} />
            <Text style={styles.secondaryButtonText}>
              {t('ai.update', 'Update')}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    borderWidth: 1,
    borderColor: colors.gray[200],
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  entityImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 12,
  },
  entityImagePlaceholder: {
    width: 60,
    height: 60,
    borderRadius: 8,
    backgroundColor: colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  entityInfo: {
    flex: 1,
  },
  entityName: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 4,
  },
  entityType: {
    fontSize: 14,
    color: colors.primary,
    textTransform: 'capitalize',
    marginBottom: 6,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 12,
    color: colors.text.secondary,
    textTransform: 'capitalize',
  },
  detailsContainer: {
    maxHeight: 200,
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
  },
  detailIcon: {
    marginRight: 8,
  },
  detailLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text.secondary,
    minWidth: 80,
  },
  detailValue: {
    fontSize: 14,
    color: colors.text.primary,
    flex: 1,
    marginLeft: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 6,
  },
  primaryButton: {
    backgroundColor: colors.primary,
  },
  secondaryButton: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  primaryButtonText: {
    color: colors.white,
    fontSize: 14,
    fontWeight: '600',
  },
  secondaryButtonText: {
    color: colors.primary,
    fontSize: 14,
    fontWeight: '600',
  },
});
