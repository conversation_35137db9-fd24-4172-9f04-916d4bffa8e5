import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  SafeAreaView,
  RefreshControl,
  Alert,
} from 'react-native';
import { Stack, router } from 'expo-router';
import { colors } from '@/constants/colors';
import { useFarmStore } from '@/store/farm-store';
import { useAuthStore } from '@/store/auth-store';
import { useTranslation } from '@/i18n/useTranslation';
import EquipmentItemCard from '@/components/EquipmentItemCard';
import {
  Tractor,
  Plus,
} from 'lucide-react-native';

interface EquipmentItem {
  id: string;
  name: string;
  category: string;
  quantity: number;
  unit: string;
  price?: number;
  supplier?: string;
  location?: string;
  description?: string;
  imageUrl?: string;
  isConsumable?: boolean;
  minQuantity?: number;
  purchaseDate?: string;
  expiryDate?: string;
  history?: any[];
  createdAt?: string;
  updatedAt?: string;
  farmId: string;
}

export default function EquipmentIndexScreen() {
  const { t, isRTL } = useTranslation();
  const { user } = useAuthStore();
  const { 
    inventoryEquipment, 
    currentFarm, 
    fetchEquipmentFromInventory, 
    isLoading 
  } = useFarmStore();
  
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'Equipment' | 'Tools'>('all');

  useEffect(() => {
    if (currentFarm?.id) {
      loadEquipment();
    }
  }, [currentFarm?.id]);

  const loadEquipment = async () => {
    if (!currentFarm?.id) return;
    
    try {
      await fetchEquipmentFromInventory(currentFarm.id);
    } catch (error) {
      console.error('Error loading equipment:', error);
      Alert.alert('Error', 'Failed to load equipment data');
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadEquipment();
    setRefreshing(false);
  };

  const getFilteredEquipment = () => {
    let filtered = inventoryEquipment || [];
    
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(item => item.category === selectedCategory);
    }
    
    if (searchQuery) {
      filtered = filtered.filter(item => 
        item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.supplier?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    
    return filtered;
  };

  const renderEquipmentItem = ({ item }: { item: EquipmentItem }) => (
    <EquipmentItemCard
      item={item}
      onPress={() => router.push(`/equipment/inventory-detail?id=${item.id}`)}
    />
  );

  const renderCategoryFilter = () => (
    <View style={styles.filterContainer}>
      {['all', 'Equipment', 'Tools'].map((category) => (
        <TouchableOpacity
          key={category}
          style={[
            styles.filterButton,
            selectedCategory === category && styles.filterButtonActive
          ]}
          onPress={() => setSelectedCategory(category as any)}
        >
          <Text style={[
            styles.filterButtonText,
            selectedCategory === category && styles.filterButtonTextActive
          ]}>
            {category === 'all' ? t('common.all', 'All') : category}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Tractor size={48} color={colors.gray[400]} />
      <Text style={styles.emptyStateTitle}>
        {t('equipment.noEquipment', 'No Equipment Found')}
      </Text>
      <Text style={styles.emptyStateText}>
        {t('equipment.noEquipmentDescription', 'Add equipment and tools to track your farm inventory')}
      </Text>
      <TouchableOpacity
        style={styles.addButton}
        onPress={() => router.push('/equipment/create')}
      >
        <Plus size={20} color={colors.white} />
        <Text style={styles.addButtonText}>
          {t('equipment.addEquipment', 'Add Equipment')}
        </Text>
      </TouchableOpacity>
    </View>
  );

  if (isLoading && !inventoryEquipment?.length) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen
          options={{
            title: t('equipment.title', 'Equipment & Tools'),
            headerRight: () => (
              <TouchableOpacity
                onPress={() => router.push('/equipment/create')}
                style={styles.headerButton}
              >
                <Plus size={24} color={colors.primary} />
              </TouchableOpacity>
            ),
          }}
        />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>
            {t('common.loading', 'Loading...')}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const filteredEquipment = getFilteredEquipment();

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen
        options={{
          title: t('equipment.title', 'Equipment & Tools'),
          headerRight: () => (
            <TouchableOpacity
              onPress={() => router.push('/equipment/create')}
              style={styles.headerButton}
            >
              <Plus size={24} color={colors.primary} />
            </TouchableOpacity>
          ),
        }}
      />

      {renderCategoryFilter()}

      <FlatList
        data={filteredEquipment}
        keyExtractor={(item) => item.id}
        renderItem={renderEquipmentItem}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
          />
        }
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.gray[600],
  },
  headerButton: {
    padding: 8,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
  },
  filterButtonActive: {
    backgroundColor: colors.primary,
  },
  filterButtonText: {
    fontSize: 14,
    color: colors.gray[700],
    fontWeight: '500',
  },
  filterButtonTextActive: {
    color: colors.white,
  },
  listContainer: {
    padding: 16,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 64,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    color: colors.gray[600],
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  addButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});
